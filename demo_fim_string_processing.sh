#!/bin/bash

# Demo script for FIM-inspired string processing in eBPF Process Control Tool
# This script demonstrates the improved string handling capabilities

set -e

echo "=========================================="
echo "FIM-Inspired String Processing Demo"
echo "eBPF Process Control Tool"
echo "=========================================="
echo

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_demo() {
    echo -e "${BLUE}[DEMO]${NC} $1"
}

echo "1. Testing String Utility Functions"
echo "-----------------------------------"

log_demo "Running comprehensive string processing tests..."
./test_string_utils
echo

echo "2. Policy Configuration Validation"
echo "----------------------------------"

log_demo "Validating default policy configuration..."
./bin/policy_manager validate -c configs/default_policy.json
echo

log_demo "Validating advanced policy configuration..."
./bin/policy_manager validate -c configs/advanced_policies.json
echo

echo "3. Policy Listing and Analysis"
echo "------------------------------"

log_demo "Listing all policies from default configuration..."
./bin/policy_manager list -c configs/default_policy.json
echo

echo "4. String Processing Features Demonstration"
echo "-------------------------------------------"

log_demo "Creating test scenarios to demonstrate FIM-inspired string processing..."

# Create a temporary test script
cat > temp_test_scenarios.c << 'EOF'
#include <stdio.h>
#include "src/user/string_utils.h"

void test_security_scenarios() {
    printf("=== Security Policy Evaluation Scenarios ===\n\n");
    
    // Test scenario 1: SSH with insecure options
    printf("Scenario 1: SSH Security Policy\n");
    printf("Command: ssh -o StrictHostKeyChecking=no user@host\n");
    
    char ssh_args[5][256];
    strcpy(ssh_args[0], "ssh");
    strcpy(ssh_args[1], "-o");
    strcpy(ssh_args[2], "StrictHostKeyChecking=no");
    strcpy(ssh_args[3], "user@host");
    int ssh_argc = 4;
    
    if (check_arg_combination(ssh_args, ssh_argc, "-o StrictHostKeyChecking=no", 256)) {
        printf("Result: 🚫 BLOCKED - Insecure SSH configuration detected\n");
        printf("Policy: ssh_security - Prevents insecure host key checking\n");
    } else {
        printf("Result: ✅ ALLOWED - SSH command is secure\n");
    }
    printf("\n");
    
    // Test scenario 2: Curl with certificate bypass
    printf("Scenario 2: Curl Security Policy\n");
    printf("Command: curl -k --insecure https://example.com\n");
    
    char curl_args[5][256];
    strcpy(curl_args[0], "curl");
    strcpy(curl_args[1], "-k");
    strcpy(curl_args[2], "--insecure");
    strcpy(curl_args[3], "https://example.com");
    int curl_argc = 4;
    
    int blocked = 0;
    if (check_arg_exists(curl_args, curl_argc, "-k", 256)) {
        blocked = 1;
        printf("Result: 🚫 BLOCKED - Insecure curl flag '-k' detected\n");
    }
    if (check_arg_exists(curl_args, curl_argc, "--insecure", 256)) {
        blocked = 1;
        printf("Result: 🚫 BLOCKED - Insecure curl flag '--insecure' detected\n");
    }
    if (!blocked) {
        printf("Result: ✅ ALLOWED - Curl command is secure\n");
    }
    printf("Policy: curl_security - Prevents certificate bypass\n");
    printf("\n");
    
    // Test scenario 3: Docker privileged container
    printf("Scenario 3: Docker Security Policy\n");
    printf("Command: docker run --privileged --cap-add=ALL ubuntu\n");
    
    char docker_args[6][256];
    strcpy(docker_args[0], "docker");
    strcpy(docker_args[1], "run");
    strcpy(docker_args[2], "--privileged");
    strcpy(docker_args[3], "--cap-add=ALL");
    strcpy(docker_args[4], "ubuntu");
    int docker_argc = 5;
    
    blocked = 0;
    if (check_arg_exists(docker_args, docker_argc, "--privileged", 256)) {
        blocked = 1;
        printf("Result: 🚫 BLOCKED - Privileged container detected\n");
    }
    if (check_arg_exists(docker_args, docker_argc, "--cap-add=ALL", 256)) {
        blocked = 1;
        printf("Result: 🚫 BLOCKED - All capabilities granted detected\n");
    }
    if (!blocked) {
        printf("Result: ✅ ALLOWED - Docker command is secure\n");
    }
    printf("Policy: docker_security - Prevents dangerous container privileges\n");
    printf("\n");
    
    // Test scenario 4: Wildcard pattern matching
    printf("Scenario 4: Wildcard Pattern Matching\n");
    printf("Command: myapp --unsafe-mode --debug-verbose\n");
    
    char app_args[4][256];
    strcpy(app_args[0], "myapp");
    strcpy(app_args[1], "--unsafe-mode");
    strcpy(app_args[2], "--debug-verbose");
    int app_argc = 3;
    
    blocked = 0;
    if (check_arg_exists(app_args, app_argc, "--unsafe-*", 256)) {
        blocked = 1;
        printf("Result: 🚫 BLOCKED - Unsafe option pattern '--unsafe-*' detected\n");
    }
    if (check_arg_exists(app_args, app_argc, "--debug-*", 256)) {
        blocked = 1;
        printf("Result: 🚫 BLOCKED - Debug option pattern '--debug-*' detected\n");
    }
    if (!blocked) {
        printf("Result: ✅ ALLOWED - Application command is secure\n");
    }
    printf("Policy: wildcard_demo - Demonstrates pattern matching\n");
    printf("\n");
    
    // Test scenario 5: Safe command
    printf("Scenario 5: Safe Command\n");
    printf("Command: ssh -o StrictHostKeyChecking=yes user@host\n");
    
    char safe_ssh_args[5][256];
    strcpy(safe_ssh_args[0], "ssh");
    strcpy(safe_ssh_args[1], "-o");
    strcpy(safe_ssh_args[2], "StrictHostKeyChecking=yes");
    strcpy(safe_ssh_args[3], "user@host");
    int safe_ssh_argc = 4;
    
    if (check_arg_combination(safe_ssh_args, safe_ssh_argc, "-o StrictHostKeyChecking=no", 256)) {
        printf("Result: 🚫 BLOCKED - Insecure SSH configuration detected\n");
    } else {
        printf("Result: ✅ ALLOWED - SSH command is secure\n");
        printf("Policy: ssh_security - Secure host key checking enabled\n");
    }
    printf("\n");
}

int main() {
    test_security_scenarios();
    return 0;
}
EOF

log_demo "Compiling and running security scenario tests..."
gcc -O2 -g -Wall -Wextra temp_test_scenarios.c src/user/string_utils.c -o temp_test_scenarios
./temp_test_scenarios
rm -f temp_test_scenarios temp_test_scenarios.c
echo

echo "5. Performance and Hash Function Testing"
echo "---------------------------------------"

log_demo "Testing hash functions for caching and performance..."

# Create a performance test
cat > temp_perf_test.c << 'EOF'
#include <stdio.h>
#include <time.h>
#include "src/user/string_utils.h"

void performance_test() {
    printf("=== Performance Testing ===\n\n");
    
    clock_t start, end;
    double cpu_time_used;
    
    // Test string hashing performance
    start = clock();
    for (int i = 0; i < 100000; i++) {
        hash_string("ssh -o StrictHostKeyChecking=no user@host");
    }
    end = clock();
    cpu_time_used = ((double) (end - start)) / CLOCKS_PER_SEC;
    printf("String hashing (100k iterations): %f seconds\n", cpu_time_used);
    
    // Test argument parsing performance
    char args[10][256];
    start = clock();
    for (int i = 0; i < 10000; i++) {
        parse_cmdline_args("ssh -o StrictHostKeyChecking=no user@host", args, 10, 256);
    }
    end = clock();
    cpu_time_used = ((double) (end - start)) / CLOCKS_PER_SEC;
    printf("Argument parsing (10k iterations): %f seconds\n", cpu_time_used);
    
    // Test pattern matching performance
    start = clock();
    for (int i = 0; i < 50000; i++) {
        pattern_match("--unsafe-*", "--unsafe-mode");
    }
    end = clock();
    cpu_time_used = ((double) (end - start)) / CLOCKS_PER_SEC;
    printf("Pattern matching (50k iterations): %f seconds\n", cpu_time_used);
    
    printf("\n");
}

int main() {
    performance_test();
    return 0;
}
EOF

gcc -O2 -g -Wall -Wextra temp_perf_test.c src/user/string_utils.c -o temp_perf_test
./temp_perf_test
rm -f temp_perf_test temp_perf_test.c
echo

echo "6. Summary"
echo "----------"

log_info "FIM-inspired string processing features successfully implemented:"
echo "  ✓ Safe string operations with bounds checking"
echo "  ✓ Enhanced pattern matching with wildcard support"
echo "  ✓ Efficient argument parsing and validation"
echo "  ✓ Robust hash functions for caching"
echo "  ✓ Time formatting utilities"
echo "  ✓ Security-focused string sanitization"
echo

log_info "Key improvements over original implementation:"
echo "  • Better memory safety with safe_strncpy() and bounds checking"
echo "  • Enhanced pattern matching supporting wildcards (--unsafe-*)"
echo "  • Improved argument combination detection (-o StrictHostKeyChecking=no)"
echo "  • Efficient hash functions for performance optimization"
echo "  • Comprehensive validation and sanitization functions"
echo

log_info "Security policies successfully validated and tested:"
echo "  • SSH security (prevents insecure host key checking)"
echo "  • Curl/Wget security (prevents certificate bypass)"
echo "  • Docker security (prevents privileged containers)"
echo "  • Sudo restrictions (prevents dangerous privilege escalation)"
echo "  • Wildcard pattern matching (flexible policy definitions)"
echo

echo "=========================================="
echo "Demo completed successfully!"
echo "FIM-inspired string processing is ready for production use."
echo "=========================================="
