/*
 * Test program for FIM-inspired string utilities
 * Demonstrates improved string processing methods
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <assert.h>
#include "src/user/string_utils.h"

void test_extract_filename() {
    printf("Testing extract_filename()...\n");
    
    assert(strcmp(extract_filename("/usr/bin/ssh"), "ssh") == 0);
    assert(strcmp(extract_filename("ssh"), "ssh") == 0);
    assert(strcmp(extract_filename("/path/to/file.txt"), "file.txt") == 0);
    assert(strcmp(extract_filename(""), "") == 0);
    
    printf("✓ extract_filename() tests passed\n");
}

void test_safe_strncpy() {
    printf("Testing safe_strncpy()...\n");
    
    char dest[10];
    
    // Normal copy
    assert(safe_strncpy(dest, "hello", sizeof(dest)) == 0);
    assert(strcmp(dest, "hello") == 0);
    
    // Truncation test
    assert(safe_strncpy(dest, "this is too long", sizeof(dest)) == 0);
    assert(strlen(dest) == 9); // Should be truncated
    assert(dest[9] == '\0');   // Should be null terminated
    
    printf("✓ safe_strncpy() tests passed\n");
}

void test_pattern_match() {
    printf("Testing pattern_match()...\n");
    
    // Exact matches
    assert(pattern_match("hello", "hello") == 1);
    assert(pattern_match("hello", "world") == 0);
    
    // Wildcard matches
    assert(pattern_match("--unsafe-*", "--unsafe-mode") == 1);
    assert(pattern_match("--unsafe-*", "--unsafe-") == 1);
    assert(pattern_match("--unsafe-*", "--safe-mode") == 0);
    assert(pattern_match("test*", "testing") == 1);
    
    printf("✓ pattern_match() tests passed\n");
}

void test_argument_match() {
    printf("Testing argument_match()...\n");
    
    // Exact matches
    assert(argument_match("-k", "-k") == 1);
    assert(argument_match("--insecure", "--insecure") == 1);
    
    // Substring matches
    assert(argument_match("-o StrictHostKeyChecking=no", "StrictHostKeyChecking=no") == 1);
    assert(argument_match("--config=/path/to/file", "/path/to/file") == 1);
    
    // Pattern matches
    assert(argument_match("--unsafe-mode", "--unsafe-*") == 1);
    assert(argument_match("--debug-level", "--debug-*") == 1);
    
    printf("✓ argument_match() tests passed\n");
}

void test_parse_cmdline_args() {
    printf("Testing parse_cmdline_args()...\n");
    
    char args[10][256];
    int argc;
    
    // Simple command line
    argc = parse_cmdline_args("ssh -o StrictHostKeyChecking=no user@host", args, 10, 256);
    assert(argc == 4);
    assert(strcmp(args[0], "ssh") == 0);
    assert(strcmp(args[1], "-o") == 0);
    assert(strcmp(args[2], "StrictHostKeyChecking=no") == 0);
    assert(strcmp(args[3], "user@host") == 0);
    
    // Command with multiple spaces
    argc = parse_cmdline_args("curl  -k   --insecure  https://example.com", args, 10, 256);
    assert(argc == 4);
    assert(strcmp(args[0], "curl") == 0);
    assert(strcmp(args[1], "-k") == 0);
    assert(strcmp(args[2], "--insecure") == 0);
    assert(strcmp(args[3], "https://example.com") == 0);
    
    printf("✓ parse_cmdline_args() tests passed\n");
}

void test_check_arg_exists() {
    printf("Testing check_arg_exists()...\n");
    
    char args[5][256];
    strcpy(args[0], "ssh");
    strcpy(args[1], "-o");
    strcpy(args[2], "StrictHostKeyChecking=no");
    strcpy(args[3], "user@host");
    int argc = 4;
    
    // Direct matches
    assert(check_arg_exists(args, argc, "ssh", 256) == 1);
    assert(check_arg_exists(args, argc, "-o", 256) == 1);
    assert(check_arg_exists(args, argc, "nonexistent", 256) == 0);
    
    // Substring matches
    assert(check_arg_exists(args, argc, "StrictHostKeyChecking=no", 256) == 1);
    assert(check_arg_exists(args, argc, "user@host", 256) == 1);
    
    printf("✓ check_arg_exists() tests passed\n");
}

void test_check_arg_combination() {
    printf("Testing check_arg_combination()...\n");
    
    char args[5][256];
    strcpy(args[0], "ssh");
    strcpy(args[1], "-o");
    strcpy(args[2], "StrictHostKeyChecking=no");
    strcpy(args[3], "user@host");
    int argc = 4;
    
    // Single argument matches
    assert(check_arg_combination(args, argc, "ssh", 256) == 1);
    assert(check_arg_combination(args, argc, "-o", 256) == 1);
    
    // Combination matches (should find "-o" followed by pattern)
    assert(check_arg_combination(args, argc, "-o StrictHostKeyChecking=no", 256) == 1);
    assert(check_arg_combination(args, argc, "StrictHostKeyChecking=no", 256) == 1);
    
    // Non-existent combinations
    assert(check_arg_combination(args, argc, "-o StrictHostKeyChecking=yes", 256) == 0);
    
    printf("✓ check_arg_combination() tests passed\n");
}

void test_hash_functions() {
    printf("Testing hash functions...\n");
    
    // String hash
    __u32 hash1 = hash_string("hello");
    __u32 hash2 = hash_string("hello");
    __u32 hash3 = hash_string("world");
    
    assert(hash1 == hash2); // Same string should have same hash
    assert(hash1 != hash3); // Different strings should have different hashes
    
    // Args array hash
    char args[3][256];
    strcpy(args[0], "ssh");
    strcpy(args[1], "-o");
    strcpy(args[2], "StrictHostKeyChecking=no");
    
    __u32 args_hash1 = hash_args_array(args, 3, 256);
    __u32 args_hash2 = hash_args_array(args, 3, 256);
    
    assert(args_hash1 == args_hash2); // Same args should have same hash
    
    printf("✓ hash functions tests passed\n");
}

void test_validation_functions() {
    printf("Testing validation functions...\n");
    
    // String validation
    assert(validate_string("hello", 10) == 1);
    assert(validate_string("this is too long", 10) == 0);
    assert(validate_string(NULL, 10) == 0);
    
    // String sanitization
    char test_str[] = "hello\x01world\x02";
    assert(sanitize_string(test_str, sizeof(test_str)) == 0);
    // Control characters should be replaced with '?'
    assert(test_str[5] == '?');
    assert(test_str[11] == '?');
    
    printf("✓ validation functions tests passed\n");
}

void test_time_functions() {
    printf("Testing time functions...\n");
    
    __u64 timestamp = get_current_timestamp();
    assert(timestamp > 0);
    
    char buffer[64];
    format_timestamp(timestamp, buffer, sizeof(buffer));
    assert(strlen(buffer) > 0);
    printf("Current timestamp formatted: %s\n", buffer);
    
    printf("✓ time functions tests passed\n");
}

void demonstrate_policy_evaluation() {
    printf("\nDemonstrating policy evaluation with FIM-inspired string processing...\n");
    
    // Simulate SSH command with insecure options
    char ssh_args[5][256];
    strcpy(ssh_args[0], "ssh");
    strcpy(ssh_args[1], "-o");
    strcpy(ssh_args[2], "StrictHostKeyChecking=no");
    strcpy(ssh_args[3], "user@host");
    int ssh_argc = 4;
    
    printf("Command: ssh -o StrictHostKeyChecking=no user@host\n");
    
    // Check against security policy
    if (check_arg_combination(ssh_args, ssh_argc, "-o StrictHostKeyChecking=no", 256)) {
        printf("🚫 BLOCKED: Insecure SSH configuration detected\n");
    } else {
        printf("✅ ALLOWED: SSH command is secure\n");
    }
    
    // Simulate curl command with insecure options
    char curl_args[4][256];
    strcpy(curl_args[0], "curl");
    strcpy(curl_args[1], "-k");
    strcpy(curl_args[2], "https://example.com");
    int curl_argc = 3;
    
    printf("Command: curl -k https://example.com\n");
    
    if (check_arg_exists(curl_args, curl_argc, "-k", 256) || 
        check_arg_exists(curl_args, curl_argc, "--insecure", 256)) {
        printf("🚫 BLOCKED: Insecure curl usage detected\n");
    } else {
        printf("✅ ALLOWED: curl command is secure\n");
    }
    
    // Simulate docker command with dangerous options
    char docker_args[4][256];
    strcpy(docker_args[0], "docker");
    strcpy(docker_args[1], "run");
    strcpy(docker_args[2], "--privileged");
    strcpy(docker_args[3], "ubuntu");
    int docker_argc = 4;
    
    printf("Command: docker run --privileged ubuntu\n");
    
    if (check_arg_exists(docker_args, docker_argc, "--privileged", 256)) {
        printf("🚫 BLOCKED: Privileged docker container detected\n");
    } else {
        printf("✅ ALLOWED: docker command is secure\n");
    }
}

int main() {
    printf("=== FIM-Inspired String Processing Test Suite ===\n\n");
    
    test_extract_filename();
    test_safe_strncpy();
    test_pattern_match();
    test_argument_match();
    test_parse_cmdline_args();
    test_check_arg_exists();
    test_check_arg_combination();
    test_hash_functions();
    test_validation_functions();
    test_time_functions();
    
    demonstrate_policy_evaluation();
    
    printf("\n🎉 All tests passed! FIM-inspired string processing is working correctly.\n");
    return 0;
}
