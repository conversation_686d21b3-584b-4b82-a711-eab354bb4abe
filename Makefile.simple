# Makefile for Simple eBPF Process Control Tool

CC := clang
ARCH := $(shell uname -m | sed 's/x86_64/x86/' | sed 's/aarch64/arm64/')

SRC_DIR := src
KERNEL_DIR := $(SRC_DIR)/kernel
USER_DIR := $(SRC_DIR)/user
BUILD_DIR := build
BIN_DIR := bin

# eBPF compilation flags
EBPF_CFLAGS := -O2 -g -Wall \
               -target bpf -D__TARGET_ARCH_$(ARCH) \
               -I/usr/include/$(shell uname -m)-linux-gnu \
               -I$(KERNEL_DIR)

# User-space compilation flags
USER_CFLAGS := -O2 -g -Wall -I$(KERNEL_DIR)
USER_LDFLAGS := -lbpf -lelf -lz -ljson-c

# Source files
EBPF_SOURCE := $(KERNEL_DIR)/args_enforcer_simple.c
USER_SOURCES := $(USER_DIR)/main_simple.c \
                $(USER_DIR)/policy_simple.c

# Target files
EBPF_TARGET := $(BUILD_DIR)/args_enforcer_simple.o
USER_TARGET := $(BIN_DIR)/args-enforcer-simple

.PHONY: all clean test install

all: $(EBPF_TARGET) $(USER_TARGET)

$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# Build eBPF program
$(EBPF_TARGET): $(EBPF_SOURCE) | $(BUILD_DIR)
	@echo "Building simple eBPF program..."
	$(CC) $(EBPF_CFLAGS) -c $< -o $@
	@echo "eBPF program built successfully"

# Build user-space program
$(USER_TARGET): $(USER_SOURCES) $(EBPF_TARGET) | $(BIN_DIR)
	@echo "Building simple user-space program..."
	$(CC) $(USER_CFLAGS) $(USER_SOURCES) -o $@ $(USER_LDFLAGS)
	@echo "User-space program built successfully"

# Test the simple implementation
test: $(EBPF_TARGET) $(USER_TARGET)
	@echo "Testing simple eBPF implementation..."
	@echo "1. Checking eBPF object file..."
	file $(EBPF_TARGET)
	@echo "2. Checking user-space binary..."
	file $(USER_TARGET)
	@echo "3. Testing policy loading..."
	$(USER_TARGET) --config configs/optimized_policy.json --list
	@echo "Simple implementation tests completed"

# Install the simple version
install: $(USER_TARGET)
	@echo "Installing simple eBPF process control tool..."
	sudo cp $(USER_TARGET) /usr/local/bin/
	sudo mkdir -p /etc/args_enforcer
	sudo cp configs/optimized_policy.json /etc/args_enforcer/
	sudo chmod +x /usr/local/bin/args-enforcer-simple
	@echo "Installation completed"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR) $(BIN_DIR)
	@echo "Clean completed"

# Check dependencies
check-deps:
	@echo "Checking dependencies..."
	@which clang > /dev/null || (echo "Error: clang not found" && exit 1)
	@pkg-config --exists libbpf || (echo "Error: libbpf not found" && exit 1)
	@pkg-config --exists json-c || (echo "Error: json-c not found" && exit 1)
	@echo "All dependencies satisfied"

# Help target
help:
	@echo "Simple eBPF Process Control Tool - Build System"
	@echo "==============================================="
	@echo ""
	@echo "Targets:"
	@echo "  all        - Build both eBPF and user-space programs"
	@echo "  test       - Test the simple implementation"
	@echo "  install    - Install the simple version system-wide"
	@echo "  clean      - Clean build artifacts"
	@echo "  check-deps - Check build dependencies"
	@echo "  help       - Show this help message"
	@echo ""
	@echo "Features:"
	@echo "  - Limited to first 5 command-line arguments"
	@echo "  - Executable-based policy storage with O(1) lookup"
	@echo "  - Separate include/exclude maps for better performance"
	@echo "  - No stack overflow issues"
	@echo ""
	@echo "Usage:"
	@echo "  make -f Makefile.simple all"
	@echo "  sudo make -f Makefile.simple test"
