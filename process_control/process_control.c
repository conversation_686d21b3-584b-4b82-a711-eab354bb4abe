#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <stdint.h>
#include <linux/limits.h>
#include <bpf/bpf.h>
#include <bpf/libbpf.h>
#include "process_control.h"

// --- USER-SPACE HASH FUNCTION (must match BPF side) ---
uint64_t hash_djb2(const char *comm, const char *arg) {
    unsigned long hash = 5381;
    int c;
    char str[PATH_MAX + 64]; // ARG_LEN is 64
    snprintf(str, sizeof(str), "%s%s", comm, arg);

    char *ptr = str;
    while ((c = *ptr++))
        hash = ((hash << 5) + hash) + c;

    return hash;
}

void handle_event(void *ctx, int cpu, void *data, __u32 size) {
    printf("Process blocked by eBPF policy.\n");
}

int main(int argc, char **argv) {
    if (argc < 3) {
        fprintf(stderr, "Usage: %s <path_to_executable> [--include <arg>...] [--exclude <arg>...]\n", argv[0]);
        return 1;
    }

    struct bpf_object *obj;
    struct bpf_program *prog;
    struct bpf_link *link = NULL;
    struct perf_buffer *pb = NULL;
    struct perf_buffer_opts pb_opts = {};
    int policy_map_fd, include_count_map_fd, events_fd;

    obj = bpf_object__open("build/process_control.bpf.o");
    if (libbpf_get_error(obj)) return 1;

    if (bpf_object__load(obj)) goto cleanup;

    policy_map_fd = bpf_object__find_map_fd_by_name(obj, "policy_map");
    include_count_map_fd = bpf_object__find_map_fd_by_name(obj, "include_count_map");
    if (policy_map_fd < 0 || include_count_map_fd < 0) goto cleanup;

    const char *comm = argv[1];
    uint32_t include_count = 0;

    for (int i = 2; i < argc; i++) {
        if (strcmp(argv[i], "--include") == 0 && i + 1 < argc) {
            uint64_t key_hash = hash_djb2(comm, argv[i + 1]);
            uint32_t rule_type = RULE_INCLUDE;
            bpf_map_update_elem(policy_map_fd, &key_hash, &rule_type, BPF_ANY);
            include_count++;
            i++;
        } else if (strcmp(argv[i], "--exclude") == 0 && i + 1 < argc) {
            uint64_t key_hash = hash_djb2(comm, argv[i + 1]);
            uint32_t rule_type = RULE_EXCLUDE;
            bpf_map_update_elem(policy_map_fd, &key_hash, &rule_type, BPF_ANY);
            i++;
        }
    }

    if (include_count > 0) {
        bpf_map_update_elem(include_count_map_fd, comm, &include_count, BPF_ANY);
    }
    printf("Policy updated for %s. Attaching eBPF program...\n", comm);

    prog = bpf_object__find_program_by_name(obj, "check_cmdline");
    if (!prog) goto cleanup;

    link = bpf_program__attach(prog);
    if (libbpf_get_error(link)) goto cleanup;

    printf("eBPF program attached. Press Ctrl+C to stop.\n");

    events_fd = bpf_object__find_map_fd_by_name(obj, "events");
    pb_opts.sample_cb = handle_event;
    pb = perf_buffer__new(events_fd, 8, &pb_opts);
    if (libbpf_get_error(pb)) goto cleanup;

    while (perf_buffer__poll(pb, 100) >= 0);

cleanup:
    bpf_link__destroy(link);
    perf_buffer__free(pb);
    bpf_object__close(obj);
    return 0;
}