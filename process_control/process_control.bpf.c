#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_core_read.h>
#include "process_control.h"

#ifndef EPERM
#define EPERM 1
#endif

#define ARG_LEN 64
#define PATH_MAX_LEN 256
#define ARGV_ITER_MAX 32

// --- BPF MAPS ---

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 4096);
    __type(key, u64);
    __type(value, u32);
} policy_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, char[PATH_MAX_LEN]);
    __type(value, u32);
} include_count_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_PERF_EVENT_ARRAY);
    __uint(key_size, sizeof(u32));
    __uint(value_size, sizeof(u32));
} events SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, u32);
    __type(value, char[PATH_MAX_LEN + ARG_LEN]);
} scratch_map SEC(".maps");

// --- HELPER - HASH FUNCTION (djb2) ---

static __always_inline u64 hash_djb2(const char *str, int len) {
    u64 hash = 5381;
    char c;
    #pragma unroll
    for (int i = 0; i < len; i++) {
        bpf_probe_read(&c, 1, str + i);
        if (c == '\0') break;
        hash = ((hash << 5) + hash) + c;
    }
    return hash;
}

// --- BPF PROGRAM ---

SEC("lsm/bprm_check_security")
int check_cmdline(struct linux_binprm *bprm) {
    u32 zero = 0;
    char *scratch = bpf_map_lookup_elem(&scratch_map, &zero);
    if (!scratch) return 0;

    const char *filename;
    bpf_core_read(&filename, sizeof(filename), &bprm->filename);
    long comm_len = bpf_probe_read_str(scratch, PATH_MAX_LEN, filename);
    
    if (comm_len <= 0) return 0;
    unsigned int u_comm_len = (unsigned int)comm_len;

    u32 *include_total = bpf_map_lookup_elem(&include_count_map, scratch);
    if (!include_total) {
        include_total = &zero;
    }

    unsigned long arg_ptr;
    bpf_core_read(&arg_ptr, sizeof(arg_ptr), &bprm->p);
    int argc;
    bpf_core_read(&argc, sizeof(argc), &bprm->argc);

    arg_ptr += u_comm_len;

    u32 includes_found = 0;

    for (int i = 1; i < argc; i++) {
        if (i >= ARGV_ITER_MAX) break;

        // THE FIX: Add a bounds check.
        if (u_comm_len >= PATH_MAX_LEN) break;

        long arg_len = bpf_probe_read_str(scratch + u_comm_len, ARG_LEN, (void *)arg_ptr);
        if (arg_len <= 0) break;

        u64 key_hash = hash_djb2(scratch, u_comm_len + arg_len);
        u32 *rule_type = bpf_map_lookup_elem(&policy_map, &key_hash);

        if (rule_type) {
            if (*rule_type == RULE_EXCLUDE) {
                bpf_printk("Blocked on exclude rule (hash)");
                bpf_perf_event_output(bprm, &events, BPF_F_CURRENT_CPU, NULL, 0);
                return -EPERM;
            } else if (*rule_type == RULE_INCLUDE) {
                includes_found++;
            }
        }
        arg_ptr += arg_len;
    }

    if (*include_total > 0 && includes_found != *include_total) {
        bpf_printk("Blocked on include rule count");
        bpf_perf_event_output(bprm, &events, BPF_F_CURRENT_CPU, NULL, 0);
        return -EPERM;
    }

    return 0;
}

char LICENSE[] SEC("license") = "GPL";
