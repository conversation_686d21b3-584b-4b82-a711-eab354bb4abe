#include <linux/bpf.h>#include <bpf/bpf_helpers.h>#include <bpf/bpf_core_read.h>
#define RULE_EXCLUDE 1#define RULE_INCLUDE 2
struct {\
    __uint(type, BPF_MAP_TYPE_HASH);\
    __uint(max_entries, 4096);\
    __type(key, __u64);\
    __type(value, __u32);\
} policy_map SEC("maps");\

struct {\
    __uint(type, BPF_MAP_TYPE_HASH);\
    __uint(max_entries, 1024);\
    __type(key, char[256]);\
    __type(value, __u32);\
} include_count_map SEC("maps");\

SEC("lsm/bprm_check_security")\
int check_cmdline(struct linux_binprm *bprm) {\
    char comm[256];\
    bpf_probe_read_str(comm, sizeof(comm), bprm->filename);\
    return 0;\
}char LICENSE[] SEC("license") = "GPL";