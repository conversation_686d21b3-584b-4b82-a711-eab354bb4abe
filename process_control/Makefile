# Makefile for Process Control Tool - Inspired by FIM Project

CC := clang
ARCH := $(shell uname -m | sed 's/x86_64/x86/' | sed 's/aarch64/arm64/')

SRC_DIR := .
BUILD_DIR := build
BIN_DIR := bin

# Removed -Werror from CFLAGS
EBPF_CFLAGS := -O2 -g -Wall \
               -target bpf -D__TARGET_ARCH_$(ARCH) \
               -I/usr/include/$(shell uname -m)-linux-gnu

USER_CFLAGS := -O2 -g -Wall
USER_LDFLAGS := -lbpf -lelf -lz

EBPF_SOURCE := $(SRC_DIR)/process_control.bpf.c
USER_SOURCE := $(SRC_DIR)/process_control.c

EBPF_TARGET := $(BUILD_DIR)/process_control.bpf.o
USER_TARGET := $(BIN_DIR)/process-ctl

.PHONY: all clean

all: $(EBPF_TARGET) $(USER_TARGET)

$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# Compile eBPF Code
$(EBPF_TARGET): $(EBPF_SOURCE) | $(BUILD_DIR)
	$(CC) $(EBPF_CFLAGS) -c $< -o $@

# Compile Userspace Code
$(USER_TARGET): $(USER_SOURCE) $(EBPF_TARGET) | $(BIN_DIR)
	$(CC) $(USER_CFLAGS) $< -o $@ $(USER_LDFLAGS)

clean:
	rm -rf $(BUILD_DIR) $(BIN_DIR)
