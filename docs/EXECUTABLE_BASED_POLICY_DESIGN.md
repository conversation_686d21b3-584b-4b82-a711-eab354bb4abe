# Executable-Based Policy Storage Design

## Overview

This document outlines the design for refactoring the eBPF policy storage mechanism from a numeric ID-based system to an executable path-based system. This change will improve policy organization and allow for more targeted enforcement.

## Current Implementation

### Current Policy Storage
- **Map Type**: `BPF_MAP_TYPE_HASH`
- **Key**: `__u32` (numeric policy ID: 0, 1, 2, ...)
- **Value**: `struct policy_rule` (contains policy configuration)
- **Lookup**: Iterates through all policy IDs (0 to MAX_POLICIES)

### Current Policy Structure
```c
struct policy_rule {
    char name[MAX_POLICY_NAME_LEN];
    char include_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int include_count;
    int exclude_count;
    int enabled;
};
```

## New Design: Executable-Based Policy Storage

### New Policy Storage
- **Map Type**: `BPF_MAP_TYPE_HASH`
- **Key**: `char[256]` (executable path: `/usr/bin/python`, `/bin/bash`, etc.)
- **Value**: `struct executable_policy` (policy rules for specific executable)
- **Lookup**: Direct lookup by executable path (O(1) instead of O(n))

### New Policy Structure
```c
struct executable_policy {
    char executable_path[256];           // Full path to executable
    char policy_name[MAX_POLICY_NAME_LEN]; // Human-readable policy name
    char include_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int include_count;
    int exclude_count;
    int enabled;
    __u64 last_updated;                  // Timestamp for policy updates
    __u32 match_count;                   // Statistics: number of matches
};
```

## Benefits

### Performance Improvements
1. **Direct Lookup**: O(1) lookup instead of O(n) iteration
2. **Reduced Processing**: Only evaluate policies for the specific executable
3. **Better Cache Locality**: Related policies are grouped by executable

### Organizational Benefits
1. **Intuitive Configuration**: Policies are organized by executable
2. **Easier Management**: Clear mapping between executables and their policies
3. **Scalability**: Can handle many executables without performance degradation

### Security Benefits
1. **Targeted Enforcement**: Policies apply only to intended executables
2. **Reduced Attack Surface**: Fewer policy evaluations per execution
3. **Clear Audit Trail**: Easy to see which executable triggered which policy

## Implementation Plan

### Phase 1: eBPF Kernel Changes
1. Update map definition to use executable path as key
2. Modify policy lookup logic to use direct hash lookup
3. Update policy evaluation to work with new structure
4. Add executable path extraction from execve context

### Phase 2: User-Space Changes
1. Update policy loading to populate executable-based map
2. Modify policy management tools to work with executable keys
3. Update configuration file format to support executable-based policies
4. Add migration tools for existing numeric-based policies

### Phase 3: Configuration Format
```json
{
  "policies": {
    "/usr/bin/ssh": {
      "name": "ssh_security",
      "description": "Enforce secure SSH connections",
      "include": ["-o StrictHostKeyChecking=yes"],
      "exclude": ["-o StrictHostKeyChecking=no", "-o UserKnownHostsFile=/dev/null"]
    },
    "/usr/bin/curl": {
      "name": "curl_security", 
      "description": "Prevent insecure curl usage",
      "include": [],
      "exclude": ["-k", "--insecure"]
    }
  }
}
```

## Compatibility Considerations

### Backward Compatibility
- Provide migration script for existing configurations
- Support both formats during transition period
- Clear deprecation timeline for numeric-based policies

### Wildcard Support (Future Enhancement)
- Support patterns like `/usr/bin/*` for executable matching
- Implement prefix matching for common executable directories
- Add regex support for complex executable path patterns

## Testing Strategy

### Unit Tests
- Test direct executable path lookup
- Verify policy evaluation with new structure
- Test edge cases (missing executables, invalid paths)

### Integration Tests
- Test with real executables (ssh, curl, wget, etc.)
- Verify performance improvements
- Test policy updates and reloads

### Performance Tests
- Benchmark lookup performance vs. current implementation
- Measure memory usage changes
- Test with large numbers of policies

## Migration Path

### Step 1: Dual Support
- Support both numeric and executable-based lookups
- Allow gradual migration of policies

### Step 2: Default to Executable-Based
- Make executable-based the default for new policies
- Provide warnings for numeric-based policies

### Step 3: Remove Numeric Support
- Remove numeric-based policy support
- Clean up legacy code

## Risk Assessment

### Low Risk
- Direct hash lookup is well-tested BPF functionality
- Executable path extraction is standard in eBPF programs

### Medium Risk
- Configuration file format changes require careful migration
- User-space tooling needs comprehensive updates

### Mitigation
- Extensive testing with real-world scenarios
- Gradual rollout with fallback options
- Clear documentation and migration guides
