#ifndef COMMON_H
#define COMMON_H

#include <linux/types.h>
#include <stdbool.h>

// Maximum lengths for various strings
#define MAX_CMDLINE_LEN 4096
#define MAX_ARG_LEN 256
#define MAX_ARGS_COUNT 64
#define MAX_POLICY_RULES 32
#define MAX_COMM_LEN 16

// Action types for policy enforcement
typedef enum {
    ACTION_ALLOW = 0,
    ACTION_DENY = 1,
    ACTION_LOG = 2
} policy_action_t;

// Policy rule types
typedef enum {
    RULE_INCLUDE = 0,  // AND relationship - all args must be present
    RULE_EXCLUDE = 1   // OR relationship - any arg triggers the rule
} rule_type_t;

// Single argument pattern for matching
struct arg_pattern {
    char pattern[MAX_ARG_LEN];
    __u32 pattern_len;
    bool is_regex;  // Future extension for regex support
};

// Policy rule structure
struct policy_rule {
    rule_type_t type;
    policy_action_t action;
    __u32 pattern_count;
    struct arg_pattern patterns[MAX_ARGS_COUNT];
    char description[128];
    bool enabled;
};

// Event data structure passed from kernel to userspace
struct process_event {
    __u32 pid;
    __u32 ppid;
    __u32 uid;
    __u32 gid;
    char comm[MAX_COMM_LEN];
    char cmdline[MAX_CMDLINE_LEN];
    __u32 cmdline_len;
    policy_action_t action;
    __u32 rule_id;
    __u64 timestamp;
};

// Statistics structure
struct monitor_stats {
    __u64 total_events;
    __u64 allowed_events;
    __u64 denied_events;
    __u64 logged_events;
    __u64 policy_matches;
};

// Configuration structure for the monitor
struct monitor_config {
    bool verbose;
    bool log_allowed;
    bool log_denied;
    char log_file[256];
    __u32 rule_count;
    struct policy_rule rules[MAX_POLICY_RULES];
};

#endif // COMMON_H
