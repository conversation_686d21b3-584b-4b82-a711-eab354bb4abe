#ifndef POLICY_H
#define POLICY_H

#include "common.h"
#include <stdio.h>

// Policy management functions
int policy_init(struct monitor_config *config);
int policy_load_from_file(struct monitor_config *config, const char *filename);
int policy_save_to_file(const struct monitor_config *config, const char *filename);
int policy_add_rule(struct monitor_config *config, const struct policy_rule *rule);
int policy_remove_rule(struct monitor_config *config, __u32 rule_id);
int policy_enable_rule(struct monitor_config *config, __u32 rule_id, bool enable);

// Policy evaluation functions
policy_action_t policy_evaluate_cmdline(const struct monitor_config *config, 
                                       const char *cmdline, __u32 *matched_rule_id);
bool policy_match_include_rule(const struct policy_rule *rule, const char *cmdline);
bool policy_match_exclude_rule(const struct policy_rule *rule, const char *cmdline);

// Utility functions
int policy_parse_cmdline_args(const char *cmdline, char args[][MAX_ARG_LEN], int max_args);
bool policy_pattern_match(const char *pattern, const char *text);
void policy_print_rule(const struct policy_rule *rule, int rule_id);
void policy_print_config(const struct monitor_config *config);

// Configuration file parsing
int policy_parse_config_line(const char *line, struct policy_rule *rule);
const char *policy_action_to_string(policy_action_t action);
const char *policy_rule_type_to_string(rule_type_t type);
policy_action_t policy_string_to_action(const char *str);
rule_type_t policy_string_to_rule_type(const char *str);

#endif // POLICY_H
