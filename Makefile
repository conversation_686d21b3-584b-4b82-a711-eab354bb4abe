# eBPF Process Control Tool Makefile
# Requires: clang, llvm, libbpf-dev, linux-headers

CC = gcc
CLANG = clang
ARCH = $(shell uname -m | sed 's/x86_64/x86/' | sed 's/aarch64/arm64/')

# Directories
SRC_DIR = src
KERN_DIR = $(SRC_DIR)/kernel
USER_DIR = $(SRC_DIR)/user
BUILD_DIR = build
BIN_DIR = bin

# Kernel source directory (adjust as needed)
KERNEL_SRC = /lib/modules/$(shell uname -r)/build

# Compiler flags
CFLAGS = -O2 -g -Wall -Wextra
KERN_CFLAGS = -O2 -g -Wall -Wextra -target bpf -D__TARGET_ARCH_$(ARCH)
KERN_CFLAGS += -I$(KERNEL_SRC)/arch/$(ARCH)/include
KERN_CFLAGS += -I$(KERNEL_SRC)/arch/$(ARCH)/include/generated
KERN_CFLAGS += -I$(KERNEL_SRC)/include
KERN_CFLAGS += -I$(KERNEL_SRC)/arch/$(ARCH)/include/uapi
KERN_CFLAGS += -I$(KERNEL_SRC)/arch/$(ARCH)/include/generated/uapi
KERN_CFLAGS += -I$(KERNEL_SRC)/include/uapi
KERN_CFLAGS += -I$(KERNEL_SRC)/include/generated/uapi
KERN_CFLAGS += -include $(KERNEL_SRC)/include/linux/kconfig.h

# Libraries
LIBS = -lbpf -lelf -lz -ljson-c -lpthread

# Source files
KERN_SOURCES = $(wildcard $(KERN_DIR)/*.c)
USER_SOURCES = $(wildcard $(USER_DIR)/*.c)

# Object files
KERN_OBJECTS = $(KERN_SOURCES:$(KERN_DIR)/%.c=$(BUILD_DIR)/%.o)
USER_OBJECTS = $(USER_SOURCES:$(USER_DIR)/%.c=$(BUILD_DIR)/%.o)

# Target binaries
TARGETS = $(BIN_DIR)/args_enforcer $(BIN_DIR)/policy_manager $(BIN_DIR)/stats_viewer

# Default target
all: directories $(TARGETS)

# Create directories
directories:
	@mkdir -p $(BUILD_DIR) $(BIN_DIR)

# Kernel eBPF objects
$(BUILD_DIR)/%.o: $(KERN_DIR)/%.c
	$(CLANG) $(KERN_CFLAGS) -c $< -o $@

# User space objects
$(BUILD_DIR)/%.o: $(USER_DIR)/%.c
	$(CC) $(CFLAGS) -c $< -o $@

# Main enforcer binary
$(BIN_DIR)/args_enforcer: $(BUILD_DIR)/main.o $(BUILD_DIR)/policy.o $(BUILD_DIR)/bpf_loader.o $(BUILD_DIR)/logging.o $(BUILD_DIR)/string_utils.o
	$(CC) $(CFLAGS) $^ -o $@ $(LIBS)

# Policy management tool
$(BIN_DIR)/policy_manager: $(BUILD_DIR)/policy_manager.o $(BUILD_DIR)/policy.o $(BUILD_DIR)/logging.o $(BUILD_DIR)/string_utils.o
	$(CC) $(CFLAGS) $^ -o $@ $(LIBS)

# Statistics viewer tool
$(BIN_DIR)/stats_viewer: $(BUILD_DIR)/stats_viewer.o $(BUILD_DIR)/logging.o
	$(CC) $(CFLAGS) $^ -o $@ $(LIBS)

# Install target
install: all
	sudo cp $(BIN_DIR)/args_enforcer /usr/local/bin/
	sudo cp $(BIN_DIR)/policy_manager /usr/local/bin/
	sudo cp $(BIN_DIR)/stats_viewer /usr/local/bin/
	sudo mkdir -p /etc/args_enforcer
	sudo cp configs/default_policy.json /etc/args_enforcer/
	sudo cp configs/advanced_policies.json /etc/args_enforcer/

# Clean target
clean:
	rm -rf $(BUILD_DIR) $(BIN_DIR)

# Development helpers
dev-setup:
	sudo apt-get update
	sudo apt-get install -y clang llvm libbpf-dev linux-headers-$(shell uname -r)
	sudo apt-get install -y libelf-dev zlib1g-dev

# Test target
test: all
	sudo ./tests/run_tests.sh

# Debug target
debug: CFLAGS += -DDEBUG -g
debug: KERN_CFLAGS += -DDEBUG
debug: all

.PHONY: all directories install clean dev-setup test debug
