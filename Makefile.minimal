# Makefile for Minimal eBPF Process Control Tool

CC := clang
ARCH := $(shell uname -m | sed 's/x86_64/x86/' | sed 's/aarch64/arm64/')

SRC_DIR := src
KERNEL_DIR := $(SRC_DIR)/kernel
USER_DIR := $(SRC_DIR)/user
BUILD_DIR := build
BIN_DIR := bin

# eBPF compilation flags
EBPF_CFLAGS := -O2 -g -Wall \
               -target bpf -D__TARGET_ARCH_$(ARCH) \
               -I/usr/include/$(shell uname -m)-linux-gnu \
               -I$(KERNEL_DIR)

# User-space compilation flags
USER_CFLAGS := -O2 -g -Wall -I$(KERNEL_DIR)
USER_LDFLAGS := -lbpf -lelf -lz -ljson-c

# Source files
EBPF_SOURCE := $(KERNEL_DIR)/args_enforcer_minimal.c
USER_SOURCES := $(USER_DIR)/main_final.c \
                $(USER_DIR)/policy_final.c

# Target files
EBPF_TARGET := $(BUILD_DIR)/args_enforcer_minimal.o
USER_TARGET := $(BIN_DIR)/args-enforcer-minimal

.PHONY: all clean test

all: $(EBPF_TARGET) $(USER_TARGET)

$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# Build eBPF program
$(EBPF_TARGET): $(EBPF_SOURCE) | $(BUILD_DIR)
	@echo "Building minimal eBPF program..."
	$(CC) $(EBPF_CFLAGS) -c $< -o $@
	@echo "eBPF program built successfully"

# Build user-space program (reuse final version but with minimal eBPF)
$(USER_TARGET): $(USER_SOURCES) $(EBPF_TARGET) | $(BIN_DIR)
	@echo "Building minimal user-space program..."
	$(CC) $(USER_CFLAGS) $(USER_SOURCES) -o $@ $(USER_LDFLAGS)
	@echo "User-space program built successfully"

# Test the minimal implementation
test: $(EBPF_TARGET) $(USER_TARGET)
	@echo "Testing minimal eBPF implementation..."
	@echo "1. Checking eBPF object file..."
	file $(EBPF_TARGET)
	@echo "2. Checking user-space binary..."
	file $(USER_TARGET)
	@echo "3. Testing policy loading..."
	sudo $(USER_TARGET) --config configs/optimized_policy.json --list
	@echo "Minimal implementation tests completed successfully!"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR) $(BIN_DIR)
	@echo "Clean completed"

# Help target
help:
	@echo "Minimal eBPF Process Control Tool - Build System"
	@echo "==============================================="
	@echo ""
	@echo "Targets:"
	@echo "  all        - Build both eBPF and user-space programs"
	@echo "  test       - Test the minimal implementation"
	@echo "  clean      - Clean build artifacts"
	@echo "  help       - Show this help message"
	@echo ""
	@echo "Minimal Version Features:"
	@echo "  - Limited to first 5 command-line arguments"
	@echo "  - Arguments stored in separate per-CPU arrays (arg1-arg5)"
	@echo "  - Executable-based policy storage with O(1) lookup"
	@echo "  - Simple exact string matching (no complex substring search)"
	@echo "  - No stack overflow issues"
	@echo "  - Minimal complexity for eBPF verifier"
	@echo ""
	@echo "Usage:"
	@echo "  make -f Makefile.minimal all"
	@echo "  sudo make -f Makefile.minimal test"
