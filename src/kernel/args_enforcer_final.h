/*
 * eBPF Process Control Tool - Final Optimized Version Header
 */

#ifndef ARGS_ENFORCER_FINAL_H
#define ARGS_ENFORCER_FINAL_H

/* Use basic types to avoid conflicts with vmlinux.h */
#ifndef __u32
typedef unsigned int __u32;
#endif
#ifndef __u64
typedef unsigned long long __u64;
#endif

/* Constants */
#define MAX_ARGS 5
#define MAX_ARG_LEN 64
#define MAX_EXECUTABLE_PATH_LEN 256
#define MAX_POLICY_STR_LEN 512
#define MAX_POLICIES 100

/* Event structure for user-space communication */
struct exec_event {
    __u32 pid;
    __u32 uid;
    char comm[16];
    char filename[MAX_EXECUTABLE_PATH_LEN];
    char arg1[MAX_ARG_LEN];
    char arg2[MAX_ARG_LEN];
    char arg3[MAX_ARG_LEN];
    char arg4[MAX_ARG_LEN];
    char arg5[MAX_ARG_LEN];
    int argc;
    int action; // 0 = allow, 1 = block
    __u64 timestamp;
};

/* Statistics structure */
struct policy_stats {
    __u64 total_executions;
    __u64 blocked_executions;
    __u64 allowed_executions;
    __u64 policy_lookups;
    __u64 policy_matches;
};

#endif /* ARGS_ENFORCER_FINAL_H */
