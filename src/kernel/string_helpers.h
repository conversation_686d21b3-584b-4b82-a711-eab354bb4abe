/*
 * eBPF String Helper Functions - Inspired by FIM project
 * Optimized string processing for kernel space
 */

#ifndef STRING_HELPERS_H
#define STRING_HELPERS_H

#define MAX_ARG_LEN 256
#define MAX_ARGS 64

/* ========== eBPF String Utilities ========== */

/**
 * Safe string length calculation for eBPF
 * @param str: String to measure
 * @param max_len: Maximum length to check
 * @return: String length or max_len if longer
 */
static __always_inline int bpf_strlen(const char *str, int max_len) {
    if (!str) return 0;
    
    #pragma unroll
    for (int i = 0; i < max_len; i++) {
        if (str[i] == '\0')
            return i;
    }
    return max_len;
}

/**
 * Safe string comparison for eBPF
 * @param s1: First string
 * @param s2: Second string
 * @param max_len: Maximum length to compare
 * @return: 0 if equal, non-zero if different
 */
static __always_inline int bpf_strcmp(const char *s1, const char *s2, int max_len) {
    if (!s1 || !s2) return -1;
    
    #pragma unroll
    for (int i = 0; i < max_len; i++) {
        if (s1[i] != s2[i])
            return s1[i] - s2[i];
        if (s1[i] == '\0')
            return 0;
    }
    return 0;
}

/**
 * Find substring in string (eBPF version)
 * @param haystack: String to search in
 * @param needle: String to search for
 * @param haystack_len: Length of haystack
 * @param needle_len: Length of needle
 * @return: 1 if found, 0 if not found
 */
static __always_inline int bpf_strstr(const char *haystack, const char *needle, 
                                     int haystack_len, int needle_len) {
    if (!haystack || !needle) return 0;
    if (needle_len == 0) return 1;
    if (needle_len > haystack_len) return 0;

    #pragma unroll
    for (int i = 0; i <= haystack_len - needle_len; i++) {
        int match = 1;
        #pragma unroll
        for (int j = 0; j < needle_len; j++) {
            if (haystack[i + j] != needle[j]) {
                match = 0;
                break;
            }
        }
        if (match) return 1;
    }
    return 0;
}

/**
 * Wildcard pattern matching for eBPF (supports * at end)
 * @param arg: Argument string
 * @param pattern: Pattern string
 * @return: 1 if match, 0 if not
 */
static __always_inline int bpf_wildcard_match(const char *arg, const char *pattern) {
    if (!arg || !pattern) return 0;
    
    int arg_len = bpf_strlen(arg, MAX_ARG_LEN);
    int pattern_len = bpf_strlen(pattern, MAX_ARG_LEN);

    if (pattern_len == 0) return 0;

    // Check if pattern ends with '*'
    if (pattern_len > 0 && pattern[pattern_len - 1] == '*') {
        // Match prefix
        int prefix_len = pattern_len - 1;
        if (arg_len >= prefix_len) {
            #pragma unroll
            for (int i = 0; i < prefix_len; i++) {
                if (arg[i] != pattern[i]) {
                    return 0;
                }
            }
            return 1;
        }
        return 0;
    }

    // Exact match if no wildcard
    return (arg_len == pattern_len && bpf_strcmp(arg, pattern, pattern_len) == 0);
}

/**
 * Enhanced argument matching with exact, partial, and wildcard matching
 * @param arg: Argument string
 * @param pattern: Pattern string
 * @return: 1 if match, 0 if not
 */
static __always_inline int bpf_argument_match(const char *arg, const char *pattern) {
    if (!arg || !pattern) return 0;
    
    int arg_len = bpf_strlen(arg, MAX_ARG_LEN);
    int pattern_len = bpf_strlen(pattern, MAX_ARG_LEN);

    if (pattern_len == 0) return 0;

    // Wildcard matching
    if (bpf_wildcard_match(arg, pattern)) {
        return 1;
    }

    // Exact match
    if (arg_len == pattern_len && bpf_strcmp(arg, pattern, pattern_len) == 0) {
        return 1;
    }

    // Substring match for patterns like "-o StrictHostKeyChecking=yes"
    if (bpf_strstr(arg, pattern, arg_len, pattern_len)) {
        return 1;
    }

    return 0;
}

/**
 * Check if argument exists in argument list with enhanced matching
 * @param args: Array of argument strings
 * @param argc: Number of arguments
 * @param target_arg: Target argument to find
 * @return: 1 if found, 0 if not
 */
static __always_inline int bpf_check_arg_exists(char args[][MAX_ARG_LEN], int argc, 
                                               const char *target_arg) {
    if (!args || !target_arg || argc <= 0) return 0;
    
    #pragma unroll
    for (int i = 0; i < argc && i < MAX_ARGS; i++) {
        if (bpf_argument_match(args[i], target_arg)) {
            return 1;
        }
    }
    return 0;
}

/**
 * Check for argument combinations (e.g., "-o" followed by "StrictHostKeyChecking=no")
 * @param args: Array of argument strings
 * @param argc: Number of arguments
 * @param target_arg: Target argument pattern
 * @return: 1 if found, 0 if not
 */
static __always_inline int bpf_check_arg_combination(char args[][MAX_ARG_LEN], int argc, 
                                                    const char *target_arg) {
    if (!args || !target_arg || argc <= 0) return 0;
    
    int target_len = bpf_strlen(target_arg, MAX_ARG_LEN);

    // Check for single argument match first
    if (bpf_check_arg_exists(args, argc, target_arg)) {
        return 1;
    }

    // Check for split arguments (e.g., "-o" "StrictHostKeyChecking=no")
    #pragma unroll
    for (int i = 0; i < argc - 1 && i < MAX_ARGS - 1; i++) {
        // Look for patterns like "-o StrictHostKeyChecking=no"
        if (bpf_strcmp(args[i], "-o", 3) == 0 && i + 1 < argc) {
            // Skip "-o " prefix if present in target
            const char *pattern = target_arg;
            if (target_len > 3 && bpf_strstr(target_arg, "-o ", 3, 3)) {
                pattern = target_arg + 3; // Skip "-o "
            }
            if (bpf_argument_match(args[i + 1], pattern)) {
                return 1;
            }
        }
    }

    return 0;
}

/**
 * Simple hash function for argument arrays (DJB2 algorithm)
 * @param args: Array of argument strings
 * @param argc: Number of arguments
 * @return: Hash value
 */
static __always_inline __u32 bpf_hash_args(char args[][MAX_ARG_LEN], int argc) {
    __u32 hash = 5381;

    #pragma unroll
    for (int i = 0; i < argc && i < MAX_ARGS; i++) {
        #pragma unroll
        for (int j = 0; j < MAX_ARG_LEN && args[i][j] != '\0'; j++) {
            hash = ((hash << 5) + hash) + args[i][j];
        }
    }

    return hash;
}

/**
 * Safe string copy for eBPF (inspired by FIM's safe_strncpy)
 * @param dest: Destination buffer
 * @param src: Source string
 * @param dest_size: Size of destination buffer
 * @return: 0 on success, -1 on error
 */
static __always_inline int bpf_safe_strcpy(char *dest, const char *src, int dest_size) {
    if (!dest || !src || dest_size <= 0) {
        return -1;
    }

    #pragma unroll
    for (int i = 0; i < dest_size - 1; i++) {
        dest[i] = src[i];
        if (src[i] == '\0') {
            return 0;
        }
    }
    
    dest[dest_size - 1] = '\0';
    return 0;
}

#endif /* STRING_HELPERS_H */
