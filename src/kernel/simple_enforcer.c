// SPDX-License-Identifier: GPL-2.0
/*
 * Simple eBPF Process Control Tool - Test Version
 * Focused on testing FIM-inspired string processing
 */

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define MAX_ARG_LEN 64
#define MAX_ARGS 8
#define MAX_POLICIES 4
#define MAX_POLICY_ARGS 4
#define MAX_POLICY_NAME_LEN 32

/* Simple policy structure */
struct simple_policy {
    char name[MAX_POLICY_NAME_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int exclude_count;
    int enabled;
};

/* Simple event structure */
struct simple_event {
    __u32 pid;
    __u32 uid;
    char comm[16];
    char filename[128];
    int action; // 0 = allow, 1 = deny
    char policy_matched[MAX_POLICY_NAME_LEN];
    __u64 timestamp;
};

/* BPF Maps */
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, MAX_POLICIES);
    __type(key, __u32);
    __type(value, struct simple_policy);
} simple_policy_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 64 * 1024);
} simple_events SEC(".maps");

/* Per-CPU array for argument storage */
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_ARGS][MAX_ARG_LEN]);
} args_storage SEC(".maps");

/* Simple string length function */
static __always_inline int simple_strlen(const char *str, int max_len) {
    #pragma unroll
    for (int i = 0; i < max_len; i++) {
        if (str[i] == '\0')
            return i;
    }
    return max_len;
}

/* Simple string comparison */
static __always_inline int simple_strcmp(const char *s1, const char *s2, int max_len) {
    #pragma unroll
    for (int i = 0; i < max_len; i++) {
        if (s1[i] != s2[i])
            return s1[i] - s2[i];
        if (s1[i] == '\0')
            return 0;
    }
    return 0;
}

/* Simple substring search */
static __always_inline int simple_strstr(const char *haystack, const char *needle, 
                                        int haystack_len, int needle_len) {
    if (needle_len == 0) return 1;
    if (needle_len > haystack_len) return 0;

    #pragma unroll
    for (int i = 0; i <= haystack_len - needle_len; i++) {
        int match = 1;
        #pragma unroll
        for (int j = 0; j < needle_len && j < 8; j++) { // Limit unroll
            if (haystack[i + j] != needle[j]) {
                match = 0;
                break;
            }
        }
        if (match) return 1;
    }
    return 0;
}

/* Check if argument matches pattern */
static __always_inline int arg_matches(const char *arg, const char *pattern) {
    int arg_len = simple_strlen(arg, MAX_ARG_LEN);
    int pattern_len = simple_strlen(pattern, MAX_ARG_LEN);

    // Exact match
    if (arg_len == pattern_len && simple_strcmp(arg, pattern, pattern_len) == 0) {
        return 1;
    }

    // Substring match
    if (simple_strstr(arg, pattern, arg_len, pattern_len)) {
        return 1;
    }

    return 0;
}

/* Check if any argument matches the exclude patterns */
static __always_inline int should_block(struct simple_policy *policy, 
                                       char args[][MAX_ARG_LEN], int argc) {
    if (!policy || !policy->enabled) return 0;

    #pragma unroll
    for (int i = 0; i < policy->exclude_count && i < MAX_POLICY_ARGS; i++) {
        if (policy->exclude_args[i][0] == '\0') continue;
        
        #pragma unroll
        for (int j = 0; j < argc && j < MAX_ARGS; j++) {
            if (arg_matches(args[j], policy->exclude_args[i])) {
                return 1; // Block
            }
        }
    }
    return 0; // Allow
}

/* Parse a few arguments from execve */
static __always_inline int parse_simple_args(struct pt_regs *ctx, char args[][MAX_ARG_LEN]) {
    const char *const *argv;
    const char *arg;
    int argc = 0;
    long ret;

    argv = (const char *const *)PT_REGS_PARM2(ctx);
    if (!argv) return 0;

    #pragma unroll
    for (int i = 0; i < MAX_ARGS; i++) {
        args[i][0] = '\0';
    }

    #pragma unroll
    for (int i = 0; i < MAX_ARGS; i++) {
        ret = bpf_probe_read_user(&arg, sizeof(arg), &argv[i]);
        if (ret != 0 || !arg) break;

        ret = bpf_probe_read_user_str(args[i], MAX_ARG_LEN, arg);
        if (ret < 0) break;

        args[i][MAX_ARG_LEN - 1] = '\0';
        argc++;
    }

    return argc;
}

/* Safe string copy */
static __always_inline void safe_copy(char *dest, const char *src, int max_len) {
    #pragma unroll
    for (int i = 0; i < max_len - 1; i++) {
        dest[i] = src[i];
        if (src[i] == '\0') return;
    }
    dest[max_len - 1] = '\0';
}

SEC("tracepoint/syscalls/sys_enter_execve")
int simple_trace_execve(struct trace_event_raw_sys_enter *ctx) {
    struct simple_event *event;
    struct simple_policy *policy;
    __u32 pid, uid;
    int argc;
    char filename[128];
    const char *filename_ptr;

    // Get per-CPU args storage
    __u32 key = 0;
    char (*args)[MAX_ARG_LEN] = bpf_map_lookup_elem(&args_storage, &key);
    if (!args) return 0;

    // Get basic info
    pid = bpf_get_current_pid_tgid() >> 32;
    uid = bpf_get_current_uid_gid() & 0xffffffff;
    
    // Parse filename
    filename_ptr = (const char *)ctx->args[0];
    if (bpf_probe_read_user_str(filename, sizeof(filename), filename_ptr) < 0)
        return 0;
    
    // Parse arguments
    argc = parse_simple_args((struct pt_regs *)ctx, args);
    if (argc <= 0) return 0;

    // Check policies
    #pragma unroll
    for (__u32 policy_id = 0; policy_id < MAX_POLICIES; policy_id++) {
        policy = bpf_map_lookup_elem(&simple_policy_map, &policy_id);
        if (!policy) continue;

        if (should_block(policy, args, argc)) {
            // Send block event
            event = bpf_ringbuf_reserve(&simple_events, sizeof(*event), 0);
            if (event) {
                event->pid = pid;
                event->uid = uid;
                event->action = 1; // Block
                event->timestamp = bpf_ktime_get_ns();

                bpf_get_current_comm(event->comm, sizeof(event->comm));
                safe_copy(event->filename, filename, sizeof(event->filename));
                safe_copy(event->policy_matched, policy->name, sizeof(event->policy_matched));

                bpf_ringbuf_submit(event, 0);
            }
            return -1; // Block execution
        }
    }
    
    return 0; // Allow execution
}

char _license[] SEC("license") = "GPL";
