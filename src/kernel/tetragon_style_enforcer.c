// SPDX-License-Identifier: GPL-2.0
/*
 * eBPF Process Control Tool - Tetragon Style Implementation
 * Based on Tetragon's approach to handle eBPF stack limitations
 */

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define MAX_POLICIES 16
#define MAX_POLICY_ARGS 8
#define MAX_POLICY_NAME_LEN 32
#define MAX_ARG_LEN 128
#define ARGS_BUFFER_SIZE 1024

/* Policy structure - simplified */
struct simple_policy {
    char name[MAX_POLICY_NAME_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int exclude_count;
    int enabled;
};

/* Event structure - minimal */
struct simple_event {
    __u32 pid;
    __u32 uid;
    char comm[16];
    char filename[64];
    int action; // 0 = allow, 1 = deny
    char policy_matched[MAX_POLICY_NAME_LEN];
    __u64 timestamp;
};

/* Arguments buffer structure for per-CPU storage */
struct args_buffer {
    char args_data[ARGS_BUFFER_SIZE];
    int args_count;
    int total_size;
};

/* BPF Maps - following Tetragon's pattern */
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, MAX_POLICIES);
    __type(key, __u32);
    __type(value, struct simple_policy);
} policy_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 64 * 1024);
} events SEC(".maps");

/* Per-CPU array for arguments storage - Tetragon style */
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct args_buffer);
} args_heap SEC(".maps");

/* Tail call map for multi-stage processing - properly configured */
struct {
    __uint(type, BPF_MAP_TYPE_PROG_ARRAY);
    __uint(max_entries, 3);
    __type(key, __u32);
    __type(value, __u32);
} tail_calls SEC(".maps");

/* Simple string matching function */
static __always_inline int simple_match(const char *arg, const char *pattern, int max_len) {
    #pragma unroll
    for (int i = 0; i < max_len; i++) {
        if (pattern[i] == '\0') {
            return 1; // Pattern matched completely
        }
        if (arg[i] == '\0') {
            return 0; // Arg ended before pattern
        }
        if (arg[i] != pattern[i]) {
            return 0; // Mismatch
        }
    }
    return 1; // Full match within max_len
}

/* Ultra-simplified policy check - minimal eBPF instructions */
static __always_inline int check_policy_violation(struct simple_policy *policy,
                                                 struct args_buffer *args_buf) {
    if (!policy || !policy->enabled || !args_buf || args_buf->total_size <= 2) return 0;

    // Only check for "-k" pattern (2 characters) in first 50 bytes
    if (args_buf->total_size > 2) {
        // Manual unroll for just 10 positions to keep it minimal
        if (args_buf->args_data[0] == '-' && args_buf->args_data[1] == 'k') return 1;
        if (args_buf->total_size > 3 && args_buf->args_data[1] == '-' && args_buf->args_data[2] == 'k') return 1;
        if (args_buf->total_size > 4 && args_buf->args_data[2] == '-' && args_buf->args_data[3] == 'k') return 1;
        if (args_buf->total_size > 5 && args_buf->args_data[3] == '-' && args_buf->args_data[4] == 'k') return 1;
        if (args_buf->total_size > 6 && args_buf->args_data[4] == '-' && args_buf->args_data[5] == 'k') return 1;
        if (args_buf->total_size > 7 && args_buf->args_data[5] == '-' && args_buf->args_data[6] == 'k') return 1;
        if (args_buf->total_size > 8 && args_buf->args_data[6] == '-' && args_buf->args_data[7] == 'k') return 1;
        if (args_buf->total_size > 9 && args_buf->args_data[7] == '-' && args_buf->args_data[8] == 'k') return 1;
        if (args_buf->total_size > 10 && args_buf->args_data[8] == '-' && args_buf->args_data[9] == 'k') return 1;
        if (args_buf->total_size > 11 && args_buf->args_data[9] == '-' && args_buf->args_data[10] == 'k') return 1;
    }

    return 0;
}

/* Read arguments into per-CPU buffer - Tetragon style */
static __always_inline int read_args_to_buffer(struct pt_regs *ctx,
                                              struct args_buffer *args_buf) {
    const char *const *argv;
    const char *arg;
    int total_copied = 0;
    long ret;

    argv = (const char *const *)PT_REGS_PARM2(ctx);
    if (!argv) return 0;

    args_buf->args_count = 0;
    args_buf->total_size = 0;

    // Manually unroll loop to avoid compiler warnings - read up to 8 arguments
    // Argument 0
    if (total_copied < ARGS_BUFFER_SIZE - MAX_ARG_LEN) {
        ret = bpf_probe_read_user(&arg, sizeof(arg), &argv[0]);
        if (ret == 0 && arg) {
            ret = bpf_probe_read_user_str(&args_buf->args_data[total_copied], MAX_ARG_LEN, arg);
            if (ret > 0) {
                int arg_len = ret - 1;
                if (arg_len > 0) {
                    total_copied += arg_len + 1;
                    args_buf->args_count++;
                }
            }
        }
    }

    // Arguments 1-7
    if (total_copied < ARGS_BUFFER_SIZE - MAX_ARG_LEN) {
        ret = bpf_probe_read_user(&arg, sizeof(arg), &argv[1]);
        if (ret == 0 && arg) {
            ret = bpf_probe_read_user_str(&args_buf->args_data[total_copied], MAX_ARG_LEN, arg);
            if (ret > 0) {
                int arg_len = ret - 1;
                if (arg_len > 0) {
                    total_copied += arg_len + 1;
                    args_buf->args_count++;
                }
            }
        }
    }

    if (total_copied < ARGS_BUFFER_SIZE - MAX_ARG_LEN) {
        ret = bpf_probe_read_user(&arg, sizeof(arg), &argv[2]);
        if (ret == 0 && arg) {
            ret = bpf_probe_read_user_str(&args_buf->args_data[total_copied], MAX_ARG_LEN, arg);
            if (ret > 0) {
                int arg_len = ret - 1;
                if (arg_len > 0) {
                    total_copied += arg_len + 1;
                    args_buf->args_count++;
                }
            }
        }
    }

    if (total_copied < ARGS_BUFFER_SIZE - MAX_ARG_LEN) {
        ret = bpf_probe_read_user(&arg, sizeof(arg), &argv[3]);
        if (ret == 0 && arg) {
            ret = bpf_probe_read_user_str(&args_buf->args_data[total_copied], MAX_ARG_LEN, arg);
            if (ret > 0) {
                int arg_len = ret - 1;
                if (arg_len > 0) {
                    total_copied += arg_len + 1;
                    args_buf->args_count++;
                }
            }
        }
    }

    args_buf->total_size = total_copied;
    return args_buf->args_count;
}

/* Policy management helper functions */
static __always_inline void safe_copy_string(char *dest, const char *src, int max_len) {
    #pragma unroll
    for (int i = 0; i < max_len - 1; i++) {
        dest[i] = src[i];
        if (src[i] == '\0') return;
    }
    dest[max_len - 1] = '\0';
}

/* Context structure for passing data between tail calls */
struct execve_ctx {
    __u32 pid;
    __u32 uid;
    char filename[64];
    char comm[16];
    __u64 timestamp;
    int args_processed;
};

/* Per-CPU array for context storage */
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct execve_ctx);
} ctx_storage SEC(".maps");

/* Stage 1: Parse basic process info and filename */
SEC("tracepoint/syscalls/sys_enter_execve")
int tetragon_style_execve(struct trace_event_raw_sys_enter *ctx) {
    __u32 zero = 0;
    const char *filename_ptr;

    // Get context storage
    struct execve_ctx *exec_ctx = bpf_map_lookup_elem(&ctx_storage, &zero);
    if (!exec_ctx) return 0;

    // Initialize context
    exec_ctx->pid = bpf_get_current_pid_tgid() >> 32;
    exec_ctx->uid = bpf_get_current_uid_gid() & 0xffffffff;
    exec_ctx->timestamp = bpf_ktime_get_ns();
    exec_ctx->args_processed = 0;

    // Get current comm
    bpf_get_current_comm(exec_ctx->comm, sizeof(exec_ctx->comm));

    // Parse filename
    filename_ptr = (const char *)ctx->args[0];
    if (bpf_probe_read_user_str(exec_ctx->filename, sizeof(exec_ctx->filename), filename_ptr) < 0)
        return 0;

    // Tail call to stage 2: argument processing
    bpf_tail_call(ctx, &tail_calls, 1);
    return 0; // Fallback if tail call fails
}

/* Stage 2: Process arguments and check policies */
SEC("tracepoint/syscalls/sys_enter_execve")
int stage2_process_args(struct trace_event_raw_sys_enter *ctx) {
    __u32 zero = 0;

    // Get context and args buffer
    struct execve_ctx *exec_ctx = bpf_map_lookup_elem(&ctx_storage, &zero);
    if (!exec_ctx) return 0;

    struct args_buffer *args_buf = bpf_map_lookup_elem(&args_heap, &zero);
    if (!args_buf) return 0;

    // Read arguments into buffer
    int argc = read_args_to_buffer((struct pt_regs *)ctx, args_buf);
    if (argc <= 0) return 0;

    exec_ctx->args_processed = argc;

    // Tail call to stage 3: policy evaluation
    bpf_tail_call(ctx, &tail_calls, 2);
    return 0; // Fallback if tail call fails
}

/* Stage 3: Policy evaluation and decision - completely unrolled */
SEC("tracepoint/syscalls/sys_enter_execve")
int stage3_policy_check(struct trace_event_raw_sys_enter *ctx) {
    (void)ctx; // Suppress unused parameter warning
    __u32 zero = 0;

    // Get context and args buffer
    struct execve_ctx *exec_ctx = bpf_map_lookup_elem(&ctx_storage, &zero);
    if (!exec_ctx) return 0;

    struct args_buffer *args_buf = bpf_map_lookup_elem(&args_heap, &zero);
    if (!args_buf) return 0;

    // Check policy 0 only to keep it simple
    __u32 policy_id = 0;
    struct simple_policy *policy = bpf_map_lookup_elem(&policy_map, &policy_id);
    if (policy && policy->enabled && check_policy_violation(policy, args_buf)) {
        // Send block event
        struct simple_event *event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
        if (event) {
            event->pid = exec_ctx->pid;
            event->uid = exec_ctx->uid;
            event->action = 1; // Block
            event->timestamp = exec_ctx->timestamp;

            // Copy comm and filename from context
            safe_copy_string(event->comm, exec_ctx->comm, sizeof(event->comm));
            safe_copy_string(event->filename, exec_ctx->filename, sizeof(event->filename));
            safe_copy_string(event->policy_matched, policy->name, sizeof(event->policy_matched));

            bpf_ringbuf_submit(event, 0);
        }
        return -1; // Block execution
    }

    return 0; // Allow execution
}

/* Policy initialization is handled from userspace via map updates */

char _license[] SEC("license") = "GPL";
