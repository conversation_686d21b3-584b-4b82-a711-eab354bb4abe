// SPDX-License-Identifier: GPL-2.0
/*
 * eBPF Process Control Tool - Optimized Version
 * Features:
 * 1. Limited to first 5 command-line arguments for performance
 * 2. Executable-based policy storage using separate include/exclude maps
 * 3. Arguments stored as space-separated strings (no spaces in individual args)
 */

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define MAX_ARGS 5  // Limited to first 5 arguments for performance
#define MAX_ARG_LEN 64  // Reduced size since no spaces in individual args
#define MAX_EXECUTABLE_PATH_LEN 256
#define MAX_POLICY_ARGS_STR_LEN 512  // Space-separated args string
#define MAX_POLICIES 100

/* Event structure for user-space communication */
struct exec_event {
    __u32 pid;
    __u32 ppid;
    __u32 uid;
    __u32 gid;
    char comm[16];
    char filename[MAX_EXECUTABLE_PATH_LEN];
    char args[MAX_ARGS][MAX_ARG_LEN];
    int argc;
    int action; // 0 = allow, 1 = deny
    char policy_matched[64];
    __u64 timestamp;
};

/* BPF Maps - Separate include/exclude policy storage */

// Include policy map: key=executable_path, value=space-separated args
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, MAX_POLICIES);
    __type(key, char[MAX_EXECUTABLE_PATH_LEN]);
    __type(value, char[MAX_POLICY_ARGS_STR_LEN]);
} include_policy_map SEC(".maps");

// Exclude policy map: key=executable_path, value=space-separated args
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, MAX_POLICIES);
    __type(key, char[MAX_EXECUTABLE_PATH_LEN]);
    __type(value, char[MAX_POLICY_ARGS_STR_LEN]);
} exclude_policy_map SEC(".maps");

// Events ring buffer
struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

// Statistics map
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 4);
    __type(key, __u32);
    __type(value, __u64);
} stats_map SEC(".maps");

/* Helper functions */
static __always_inline int str_contains(const char *haystack, const char *needle) {
    // Simple string contains check for space-separated args
    for (int i = 0; i < MAX_POLICY_ARGS_STR_LEN - MAX_ARG_LEN; i++) {
        if (haystack[i] == '\0') break;

        // Check if we found the needle at position i
        int match = 1;
        for (int j = 0; j < MAX_ARG_LEN && needle[j] != '\0'; j++) {
            if (haystack[i + j] != needle[j]) {
                match = 0;
                break;
            }
        }

        if (match) {
            // Check if it's a complete word (preceded and followed by space or boundary)
            int is_word_start = (i == 0 || haystack[i-1] == ' ');
            int needle_len = 0;
            for (int k = 0; k < MAX_ARG_LEN && needle[k] != '\0'; k++) needle_len++;
            int is_word_end = (haystack[i + needle_len] == ' ' || haystack[i + needle_len] == '\0');

            if (is_word_start && is_word_end) {
                return 1;
            }
        }
    }
    return 0;
}

/* Check if any argument matches the policy string */
static __always_inline int check_args_against_policy(char args[MAX_ARGS][MAX_ARG_LEN],
                                                     int argc, const char *policy_str) {
    if (!policy_str || policy_str[0] == '\0') return 0;

    // Check each argument against the policy string
    for (int i = 0; i < argc && i < MAX_ARGS; i++) {
        if (args[i][0] != '\0' && str_contains(policy_str, args[i])) {
            return 1;
        }
    }
    return 0;
}

SEC("tracepoint/syscalls/sys_enter_execve")
int trace_execve(struct trace_event_raw_sys_enter *ctx) {
    struct exec_event *event;
    __u32 pid, uid;
    char filename[MAX_EXECUTABLE_PATH_LEN] = {0};
    char args[MAX_ARGS][MAX_ARG_LEN] = {0};
    char *include_policy, *exclude_policy;
    int argc = 0;
    int action = 0; // 0 = allow, 1 = block
    const char *filename_ptr;
    const char *const *argv;
    const char *arg;

    // Get basic process info
    pid = bpf_get_current_pid_tgid() >> 32;
    uid = bpf_get_current_uid_gid() & 0xffffffff;

    // Get filename
    filename_ptr = (const char *)ctx->args[0];
    if (bpf_probe_read_user_str(filename, sizeof(filename), filename_ptr) < 0)
        return 0;

    // Get argv
    argv = (const char *const *)ctx->args[1];
    if (!argv) return 0;

    // Parse first 5 arguments only
    for (int i = 0; i < MAX_ARGS; i++) {
        if (bpf_probe_read_user(&arg, sizeof(arg), &argv[i]) != 0 || !arg)
            break;
        if (bpf_probe_read_user_str(args[i], MAX_ARG_LEN, arg) < 0)
            break;
        argc++;
    }

    // Look up policies
    include_policy = bpf_map_lookup_elem(&include_policy_map, filename);
    exclude_policy = bpf_map_lookup_elem(&exclude_policy_map, filename);

    // Check exclude policy first (if any arg matches, block)
    if (exclude_policy && exclude_policy[0] != '\0') {
        if (check_args_against_policy(args, argc, exclude_policy)) {
            action = 1; // Block
        }
    }

    // Check include policy (if specified and no args match, block)
    if (!action && include_policy && include_policy[0] != '\0') {
        if (!check_args_against_policy(args, argc, include_policy)) {
            action = 1; // Block - required args not found
        }
    }

    // Send event to user space if action was taken
    if (action) {
        event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
        if (event) {
            event->pid = pid;
            event->ppid = 0;
            event->uid = uid;
            event->gid = 0;
            event->argc = argc;
            event->action = action;
            event->timestamp = bpf_ktime_get_ns();

            bpf_get_current_comm(event->comm, sizeof(event->comm));
            __builtin_memcpy(event->filename, filename, sizeof(event->filename));
            __builtin_memcpy(event->policy_matched, "blocked", 8);

            // Copy arguments
            for (int i = 0; i < argc && i < MAX_ARGS; i++) {
                __builtin_memcpy(event->args[i], args[i], MAX_ARG_LEN);
            }

            bpf_ringbuf_submit(event, 0);
        }
    }

    // Update statistics
    __u32 stat_key = 0;
    __u64 *total_count = bpf_map_lookup_elem(&stats_map, &stat_key);
    if (total_count) {
        __sync_fetch_and_add(total_count, 1);
    }

    return action ? -1 : 0; // Block if action=1, allow if action=0
}

char _license[] SEC("license") = "GPL";
