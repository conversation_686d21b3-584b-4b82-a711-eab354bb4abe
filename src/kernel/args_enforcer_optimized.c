// SPDX-License-Identifier: GPL-2.0
/*
 * eBPF Process Control Tool - Optimized Version
 * Features:
 * 1. Limited to first 5 command-line arguments for performance
 * 2. Executable-based policy storage for O(1) lookup
 */

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define MAX_ARGS 5  // Reduced from 64 to 5 for performance optimization
#define MAX_ARG_LEN 256
#define MAX_EXECUTABLE_PATH_LEN 256
#define MAX_POLICY_ARGS 16  // Reduced from 32
#define MAX_POLICY_NAME_LEN 64
#define MAX_POLICIES 100

/* Executable-based policy structure */
struct executable_policy {
    char executable_path[MAX_EXECUTABLE_PATH_LEN];
    char policy_name[MAX_POLICY_NAME_LEN];
    char include_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int include_count;
    int exclude_count;
    int enabled;
    __u64 last_updated;
    __u32 match_count;
};

/* Event structure for user-space communication */
struct exec_event {
    __u32 pid;
    __u32 ppid;
    __u32 uid;
    __u32 gid;
    char comm[16];
    char filename[MAX_EXECUTABLE_PATH_LEN];
    char args[MAX_ARGS][MAX_ARG_LEN];
    int argc;
    int action; // 0 = allow, 1 = deny
    char policy_matched[MAX_POLICY_NAME_LEN];
    __u64 timestamp;
};

/* BPF Maps - Executable-based policy storage */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, MAX_POLICIES);
    __type(key, char[MAX_EXECUTABLE_PATH_LEN]);  // Executable path as key
    __type(value, struct executable_policy);
} executable_policy_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

/* Statistics map */
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, __u64);
} stats_map SEC(".maps");

/* Per-CPU array for temporary argument storage */
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_ARGS][MAX_ARG_LEN]);
} temp_args_map SEC(".maps");

/* Helper function to compare strings */
static __always_inline int bpf_strcmp(const char *s1, const char *s2, int max_len) {
    #pragma unroll
    for (int i = 0; i < max_len; i++) {
        if (s1[i] != s2[i])
            return s1[i] - s2[i];
        if (s1[i] == '\0')
            return 0;
    }
    return 0;
}

/* Helper function to get string length */
static __always_inline int bpf_strlen(const char *str, int max_len) {
    #pragma unroll
    for (int i = 0; i < max_len; i++) {
        if (str[i] == '\0')
            return i;
    }
    return max_len;
}

/* Enhanced argument matching with exact and wildcard matching */
static __always_inline int match_argument(const char *arg, const char *pattern) {
    int arg_len = bpf_strlen(arg, MAX_ARG_LEN);
    int pattern_len = bpf_strlen(pattern, MAX_ARG_LEN);

    if (pattern_len == 0) return 0;

    // Exact match
    if (arg_len == pattern_len && bpf_strcmp(arg, pattern, pattern_len) == 0) {
        return 1;
    }

    // Simple wildcard matching (ends with *)
    if (pattern_len > 0 && pattern[pattern_len - 1] == '*') {
        int prefix_len = pattern_len - 1;
        if (arg_len >= prefix_len) {
            return bpf_strcmp(arg, pattern, prefix_len) == 0;
        }
    }

    return 0;
}

/* Check if argument exists in argument list */
static __always_inline int check_arg_exists(char args[][MAX_ARG_LEN], int argc, const char *target_arg) {
    #pragma unroll
    for (int i = 0; i < argc && i < MAX_ARGS; i++) {
        if (match_argument(args[i], target_arg)) {
            return 1;
        }
    }
    return 0;
}

/* Evaluate policy against arguments */
static __always_inline int evaluate_policy(struct executable_policy *policy, 
                                          char args[][MAX_ARG_LEN], int argc) {
    if (!policy || !policy->enabled) return 0;

    // Check include rules (AND logic - all must be present)
    #pragma unroll
    for (int i = 0; i < policy->include_count && i < MAX_POLICY_ARGS; i++) {
        if (policy->include_args[i][0] != '\0') {
            if (!check_arg_exists(args, argc, policy->include_args[i])) {
                return 1; // Block - required argument missing
            }
        }
    }

    // Check exclude rules (OR logic - any present blocks execution)
    #pragma unroll
    for (int i = 0; i < policy->exclude_count && i < MAX_POLICY_ARGS; i++) {
        if (policy->exclude_args[i][0] != '\0') {
            if (check_arg_exists(args, argc, policy->exclude_args[i])) {
                return 1; // Block - forbidden argument present
            }
        }
    }

    return 0; // Allow
}

/* Parse command line arguments from execve - limited to first 5 args */
static __always_inline int parse_args(struct pt_regs *ctx, char args[][MAX_ARG_LEN]) {
    const char *const *argv;
    const char *arg;
    int argc = 0;
    long ret;

    argv = (const char *const *)PT_REGS_PARM2(ctx);
    if (!argv) return 0;

    // Initialize args array
    #pragma unroll
    for (int i = 0; i < MAX_ARGS; i++) {
        args[i][0] = '\0';
    }

    // Parse only first MAX_ARGS (5) arguments
    #pragma unroll
    for (int i = 0; i < MAX_ARGS; i++) {
        // Read argv[i] pointer
        ret = bpf_probe_read_user(&arg, sizeof(arg), &argv[i]);
        if (ret != 0 || !arg)
            break;

        // Read the actual argument string
        ret = bpf_probe_read_user_str(args[i], MAX_ARG_LEN, arg);
        if (ret < 0)
            break;

        // Ensure null termination
        args[i][MAX_ARG_LEN - 1] = '\0';
        argc++;
    }

    return argc;
}

/* Extract executable path from filename */
static __always_inline void extract_executable_path(const char *filename, 
                                                   char *executable_path, 
                                                   int max_len) {
    int len = bpf_strlen(filename, max_len - 1);
    
    #pragma unroll
    for (int i = 0; i < len && i < max_len - 1; i++) {
        executable_path[i] = filename[i];
    }
    executable_path[len] = '\0';
}

SEC("tracepoint/syscalls/sys_enter_execve")
int trace_execve(struct trace_event_raw_sys_enter *ctx) {
    struct exec_event *event;
    struct executable_policy *policy;
    struct task_struct *task;
    __u32 pid, uid, gid;
    int argc, action = 0;
    char args[MAX_ARGS][MAX_ARG_LEN];
    char filename[MAX_EXECUTABLE_PATH_LEN];
    char executable_path[MAX_EXECUTABLE_PATH_LEN];
    __u64 current_time;
    const char *filename_ptr;

    // Get current task info
    task = (struct task_struct *)bpf_get_current_task();
    if (!task) return 0;

    current_time = bpf_ktime_get_ns();
    pid = bpf_get_current_pid_tgid() >> 32;
    uid = bpf_get_current_uid_gid() & 0xffffffff;
    gid = bpf_get_current_uid_gid() >> 32;
    
    // Parse filename
    filename_ptr = (const char *)ctx->args[0];
    if (bpf_probe_read_user_str(filename, sizeof(filename), filename_ptr) < 0)
        return 0;
    
    // Extract executable path for policy lookup
    extract_executable_path(filename, executable_path, sizeof(executable_path));
    
    // Parse arguments (limited to first 5)
    argc = parse_args((struct pt_regs *)ctx, args);
    if (argc <= 0) return 0;

    // Direct policy lookup by executable path - O(1) operation!
    policy = bpf_map_lookup_elem(&executable_policy_map, executable_path);
    if (policy && policy->enabled) {
        // Update match counter
        __sync_fetch_and_add(&policy->match_count, 1);
        
        if (evaluate_policy(policy, args, argc)) {
            action = 1; // Block
            
            // Send block event
            event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
            if (event) {
                event->pid = pid;
                event->ppid = BPF_CORE_READ(task, real_parent, pid);
                event->uid = uid;
                event->gid = gid;
                event->argc = argc;
                event->action = action;
                event->timestamp = current_time;

                bpf_get_current_comm(event->comm, sizeof(event->comm));
                bpf_probe_read_kernel_str(event->filename, sizeof(event->filename), filename);
                bpf_probe_read_kernel_str(event->policy_matched, sizeof(event->policy_matched), policy->policy_name);

                // Copy arguments
                #pragma unroll
                for (int i = 0; i < argc && i < MAX_ARGS; i++) {
                    bpf_probe_read_kernel_str(event->args[i], MAX_ARG_LEN, args[i]);
                }

                bpf_ringbuf_submit(event, 0);
            }

            return -1; // Block execution
        }
    }

    // Allow execution - optionally send allow event for monitoring
    return 0;
}

char _license[] SEC("license") = "GPL";
