// SPDX-License-Identifier: GPL-2.0
/*
 * eBPF Process Control Tool - Minimal Working Version
 * Features:
 * 1. Limited to first 5 command-line arguments for performance
 * 2. Executable-based policy storage using separate include/exclude maps
 * 3. Simple exact string matching (no complex substring search)
 */

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define MAX_ARGS 5
#define MAX_ARG_LEN 64
#define MAX_EXECUTABLE_PATH_LEN 256
#define MAX_POLICY_STR_LEN 512
#define MAX_POLICIES 100

/* Event structure for user-space communication */
struct exec_event {
    __u32 pid;
    __u32 uid;
    char comm[16];
    char filename[MAX_EXECUTABLE_PATH_LEN];
    char arg1[MAX_ARG_LEN];
    char arg2[MAX_ARG_LEN];
    char arg3[MAX_ARG_LEN];
    char arg4[MAX_ARG_LEN];
    char arg5[MAX_ARG_LEN];
    int argc;
    int action; // 0 = allow, 1 = block
    __u64 timestamp;
};

/* BPF Maps */

// Include policy map: key=executable_path, value=space-separated args
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, MAX_POLICIES);
    __type(key, char[MAX_EXECUTABLE_PATH_LEN]);
    __type(value, char[MAX_POLICY_STR_LEN]);
} include_policy_map SEC(".maps");

// Exclude policy map: key=executable_path, value=space-separated args  
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, MAX_POLICIES);
    __type(key, char[MAX_EXECUTABLE_PATH_LEN]);
    __type(value, char[MAX_POLICY_STR_LEN]);
} exclude_policy_map SEC(".maps");

// Events ring buffer
struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

// Statistics map
struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 4);
    __type(key, __u32);
    __type(value, __u64);
} stats_map SEC(".maps");

// Per-CPU arrays to avoid stack overflow
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_EXECUTABLE_PATH_LEN]);
} temp_filename SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_ARG_LEN]);
} temp_arg1 SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_ARG_LEN]);
} temp_arg2 SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_ARG_LEN]);
} temp_arg3 SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_ARG_LEN]);
} temp_arg4 SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_ARG_LEN]);
} temp_arg5 SEC(".maps");

/* Simple string comparison - exact match only */
static __always_inline int str_equal(const char *s1, const char *s2, int max_len) {
    for (int i = 0; i < max_len; i++) {
        if (s1[i] != s2[i]) return 0;
        if (s1[i] == '\0') return 1;
    }
    return 1;
}

/* Check if argument exists in space-separated policy string */
static __always_inline int arg_in_policy_simple(const char *policy_str, const char *arg) {
    if (!policy_str || !arg || policy_str[0] == '\0' || arg[0] == '\0')
        return 0;

    // Get argument length
    int arg_len = 0;
    for (int i = 0; i < MAX_ARG_LEN && arg[i] != '\0'; i++) {
        arg_len++;
    }
    if (arg_len == 0) return 0;

    // Search for argument in policy string
    for (int i = 0; i < MAX_POLICY_STR_LEN - arg_len; i++) {
        if (policy_str[i] == '\0') break;

        // Check if argument matches at position i
        int match = 1;
        for (int j = 0; j < arg_len; j++) {
            if (policy_str[i + j] != arg[j]) {
                match = 0;
                break;
            }
        }

        if (match) {
            // Check word boundaries (space or start/end)
            int is_word_start = (i == 0 || policy_str[i-1] == ' ');
            int is_word_end = (policy_str[i + arg_len] == ' ' || policy_str[i + arg_len] == '\0');

            if (is_word_start && is_word_end) {
                return 1;
            }
        }
    }
    return 0;
}

SEC("tracepoint/syscalls/sys_enter_execve")
int trace_execve(struct trace_event_raw_sys_enter *ctx) {
    struct exec_event *event;
    __u32 pid, uid;
    char *filename, *arg1, *arg2, *arg3, *arg4, *arg5;
    char *include_policy, *exclude_policy;
    int argc = 0;
    int action = 0; // 0 = allow, 1 = block
    const char *filename_ptr;
    const char *const *argv;
    const char *arg;
    __u32 zero = 0;

    // Get per-CPU storage
    filename = bpf_map_lookup_elem(&temp_filename, &zero);
    if (!filename) return 0;
    arg1 = bpf_map_lookup_elem(&temp_arg1, &zero);
    if (!arg1) return 0;
    arg2 = bpf_map_lookup_elem(&temp_arg2, &zero);
    if (!arg2) return 0;
    arg3 = bpf_map_lookup_elem(&temp_arg3, &zero);
    if (!arg3) return 0;
    arg4 = bpf_map_lookup_elem(&temp_arg4, &zero);
    if (!arg4) return 0;
    arg5 = bpf_map_lookup_elem(&temp_arg5, &zero);
    if (!arg5) return 0;

    // Initialize
    __builtin_memset(filename, 0, MAX_EXECUTABLE_PATH_LEN);
    __builtin_memset(arg1, 0, MAX_ARG_LEN);
    __builtin_memset(arg2, 0, MAX_ARG_LEN);
    __builtin_memset(arg3, 0, MAX_ARG_LEN);
    __builtin_memset(arg4, 0, MAX_ARG_LEN);
    __builtin_memset(arg5, 0, MAX_ARG_LEN);

    // Get basic process info
    pid = bpf_get_current_pid_tgid() >> 32;
    uid = bpf_get_current_uid_gid() & 0xffffffff;
    
    // Get filename
    filename_ptr = (const char *)ctx->args[0];
    if (bpf_probe_read_user_str(filename, MAX_EXECUTABLE_PATH_LEN, filename_ptr) < 0)
        return 0;
    
    // Get argv
    argv = (const char *const *)ctx->args[1];
    if (!argv) return 0;

    // Parse first 5 arguments only - store in separate per-CPU arrays
    if (bpf_probe_read_user(&arg, sizeof(arg), &argv[0]) == 0 && arg) {
        if (bpf_probe_read_user_str(arg1, MAX_ARG_LEN, arg) >= 0) argc = 1;
    }
    if (argc >= 1 && bpf_probe_read_user(&arg, sizeof(arg), &argv[1]) == 0 && arg) {
        if (bpf_probe_read_user_str(arg2, MAX_ARG_LEN, arg) >= 0) argc = 2;
    }
    if (argc >= 2 && bpf_probe_read_user(&arg, sizeof(arg), &argv[2]) == 0 && arg) {
        if (bpf_probe_read_user_str(arg3, MAX_ARG_LEN, arg) >= 0) argc = 3;
    }
    if (argc >= 3 && bpf_probe_read_user(&arg, sizeof(arg), &argv[3]) == 0 && arg) {
        if (bpf_probe_read_user_str(arg4, MAX_ARG_LEN, arg) >= 0) argc = 4;
    }
    if (argc >= 4 && bpf_probe_read_user(&arg, sizeof(arg), &argv[4]) == 0 && arg) {
        if (bpf_probe_read_user_str(arg5, MAX_ARG_LEN, arg) >= 0) argc = 5;
    }

    // Look up policies
    include_policy = bpf_map_lookup_elem(&include_policy_map, filename);
    exclude_policy = bpf_map_lookup_elem(&exclude_policy_map, filename);

    // Check exclude policy first (if any arg matches, block)
    if (exclude_policy && exclude_policy[0] != '\0') {
        if ((argc >= 1 && arg1[0] != '\0' && arg_in_policy_simple(exclude_policy, arg1)) ||
            (argc >= 2 && arg2[0] != '\0' && arg_in_policy_simple(exclude_policy, arg2)) ||
            (argc >= 3 && arg3[0] != '\0' && arg_in_policy_simple(exclude_policy, arg3)) ||
            (argc >= 4 && arg4[0] != '\0' && arg_in_policy_simple(exclude_policy, arg4)) ||
            (argc >= 5 && arg5[0] != '\0' && arg_in_policy_simple(exclude_policy, arg5))) {
            action = 1; // Block - found excluded argument
        }
    }

    // Check include policy (if specified and no args match, block)
    if (!action && include_policy && include_policy[0] != '\0') {
        int found_required = 0;
        if ((argc >= 1 && arg1[0] != '\0' && arg_in_policy_simple(include_policy, arg1)) ||
            (argc >= 2 && arg2[0] != '\0' && arg_in_policy_simple(include_policy, arg2)) ||
            (argc >= 3 && arg3[0] != '\0' && arg_in_policy_simple(include_policy, arg3)) ||
            (argc >= 4 && arg4[0] != '\0' && arg_in_policy_simple(include_policy, arg4)) ||
            (argc >= 5 && arg5[0] != '\0' && arg_in_policy_simple(include_policy, arg5))) {
            found_required = 1;
        }
        if (!found_required) {
            action = 1; // Block - required arguments not found
        }
    }

    // Send event to user space if action was taken
    if (action) {
        event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
        if (event) {
            event->pid = pid;
            event->uid = uid;
            event->argc = argc;
            event->action = action;
            event->timestamp = bpf_ktime_get_ns();

            bpf_get_current_comm(event->comm, sizeof(event->comm));
            __builtin_memcpy(event->filename, filename, sizeof(event->filename));
            __builtin_memcpy(event->arg1, arg1, MAX_ARG_LEN);
            __builtin_memcpy(event->arg2, arg2, MAX_ARG_LEN);
            __builtin_memcpy(event->arg3, arg3, MAX_ARG_LEN);
            __builtin_memcpy(event->arg4, arg4, MAX_ARG_LEN);
            __builtin_memcpy(event->arg5, arg5, MAX_ARG_LEN);

            bpf_ringbuf_submit(event, 0);
        }
    }

    // Update statistics
    __u32 stat_key = 0;
    __u64 *total_count = bpf_map_lookup_elem(&stats_map, &stat_key);
    if (total_count) {
        __sync_fetch_and_add(total_count, 1);
    }

    return action ? -1 : 0; // Block if action=1, allow if action=0
}

char _license[] SEC("license") = "GPL";
