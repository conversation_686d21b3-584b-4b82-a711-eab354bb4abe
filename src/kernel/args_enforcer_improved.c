// SPDX-License-Identifier: GPL-2.0
/*
 * eBPF Process Control Tool - Improved Kernel Space
 * Command-line argument policy enforcement with FIM-inspired string processing
 */

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>
#include "string_helpers.h"

#define MAX_POLICIES 32
#define MAX_POLICY_ARGS 16
#define MAX_POLICY_NAME_LEN 64

/* Policy structure for eBPF maps */
struct policy_rule {
    char name[MAX_POLICY_NAME_LEN];
    char include_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int include_count;
    int exclude_count;
    int enabled;
};

/* Event structure for user-space communication */
struct exec_event {
    __u32 pid;
    __u32 ppid;
    __u32 uid;
    __u32 gid;
    char comm[16];
    char filename[256];
    int argc;
    int action; // 0 = allow, 1 = deny
    char policy_matched[MAX_POLICY_NAME_LEN];
    __u64 timestamp;
};

/* BPF Maps */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, MAX_POLICIES);
    __type(key, __u32);
    __type(value, struct policy_rule);
} policy_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

/* Per-CPU array for temporary argument storage to avoid stack overflow */
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_ARGS][MAX_ARG_LEN]);
} temp_args_map SEC(".maps");

/* Statistics structure */
struct stats {
    __u64 total_execve_calls;
    __u64 blocked_executions;
    __u64 allowed_executions;
    __u64 policy_evaluations;
};

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct stats);
} stats_map SEC(".maps");

/* Cache for recent policy decisions */
struct cache_entry {
    __u32 hash;
    __u32 decision;  // 0 = allow, 1 = block
    __u64 timestamp;
};

struct {
    __uint(type, BPF_MAP_TYPE_LRU_HASH);
    __uint(max_entries, 512);
    __type(key, __u32);
    __type(value, struct cache_entry);
} decision_cache SEC(".maps");

/* Evaluate policy against arguments using improved string processing */
static __always_inline int evaluate_policy(struct policy_rule *policy, 
                                          char args[][MAX_ARG_LEN], int argc) {
    if (!policy || !args || argc <= 0) return 0;
    
    // Check include rules (AND logic - all must be present)
    #pragma unroll
    for (int i = 0; i < policy->include_count && i < MAX_POLICY_ARGS; i++) {
        if (policy->include_args[i][0] != '\0') {
            if (!bpf_check_arg_combination(args, argc, policy->include_args[i])) {
                return 1; // Block - required argument missing
            }
        }
    }

    // Check exclude rules (OR logic - any present blocks execution)
    #pragma unroll
    for (int i = 0; i < policy->exclude_count && i < MAX_POLICY_ARGS; i++) {
        if (policy->exclude_args[i][0] != '\0') {
            if (bpf_check_arg_combination(args, argc, policy->exclude_args[i])) {
                return 1; // Block - forbidden argument present
            }
        }
    }

    return 0; // Allow
}

/* Parse command line arguments from execve with improved error handling */
static __always_inline int parse_args(struct pt_regs *ctx, char args[][MAX_ARG_LEN]) {
    const char *const *argv;
    const char *arg;
    int argc = 0;
    long ret;

    argv = (const char *const *)PT_REGS_PARM2(ctx);
    if (!argv) return 0;

    // Initialize args array
    #pragma unroll
    for (int i = 0; i < MAX_ARGS; i++) {
        args[i][0] = '\0';
    }

    #pragma unroll
    for (int i = 0; i < MAX_ARGS; i++) {
        // Read argv[i] pointer
        ret = bpf_probe_read_user(&arg, sizeof(arg), &argv[i]);
        if (ret != 0 || !arg) break;

        // Read the actual argument string using safe copy
        ret = bpf_probe_read_user_str(args[i], MAX_ARG_LEN, arg);
        if (ret < 0) break;

        // Ensure null termination
        args[i][MAX_ARG_LEN - 1] = '\0';
        argc++;
    }

    return argc;
}

/* Check decision cache */
static __always_inline int check_cache(__u32 hash, __u64 current_time) {
    struct cache_entry *entry;

    entry = bpf_map_lookup_elem(&decision_cache, &hash);
    if (entry && (current_time - entry->timestamp) < 60000000000ULL) { // 60 seconds cache
        return entry->decision;
    }

    return -1; // Cache miss
}

/* Update decision cache */
static __always_inline void update_cache(__u32 hash, int decision, __u64 timestamp) {
    struct cache_entry entry = {
        .hash = hash,
        .decision = decision,
        .timestamp = timestamp
    };

    bpf_map_update_elem(&decision_cache, &hash, &entry, BPF_ANY);
}

/* Update statistics */
static __always_inline void update_stats(int blocked) {
    __u32 key = 0;
    struct stats *stats_ptr;

    stats_ptr = bpf_map_lookup_elem(&stats_map, &key);
    if (stats_ptr) {
        __sync_fetch_and_add(&stats_ptr->total_execve_calls, 1);
        if (blocked) {
            __sync_fetch_and_add(&stats_ptr->blocked_executions, 1);
        } else {
            __sync_fetch_and_add(&stats_ptr->allowed_executions, 1);
        }
    }
}

SEC("tracepoint/syscalls/sys_enter_execve")
int trace_execve(struct trace_event_raw_sys_enter *ctx) {
    struct exec_event *event;
    struct policy_rule *policy;
    struct task_struct *task;
    __u32 pid, uid, gid;
    int argc, action = 0;
    char filename[256];
    __u64 current_time;
    __u32 args_hash;
    int cached_decision;
    const char *filename_ptr;

    // Get per-CPU args storage
    __u32 key = 0;
    char (*args)[MAX_ARG_LEN] = bpf_map_lookup_elem(&temp_args_map, &key);
    if (!args) return 0;

    // Get current time
    current_time = bpf_ktime_get_ns();

    // Get current task info
    task = (struct task_struct *)bpf_get_current_task();
    pid = bpf_get_current_pid_tgid() >> 32;
    uid = bpf_get_current_uid_gid() & 0xffffffff;
    gid = bpf_get_current_uid_gid() >> 32;
    
    // Parse filename
    filename_ptr = (const char *)ctx->args[0];
    if (bpf_probe_read_user_str(filename, sizeof(filename), filename_ptr) < 0)
        return 0;
    
    // Parse arguments
    argc = parse_args((struct pt_regs *)ctx, args);
    if (argc <= 0) return 0;

    // Calculate hash for caching
    args_hash = bpf_hash_args(args, argc);

    // Check cache first
    cached_decision = check_cache(args_hash, current_time);
    if (cached_decision >= 0) {
        update_stats(cached_decision);
        return cached_decision == 1 ? -1 : 0;
    }

    // Check policies
    #pragma unroll
    for (__u32 policy_id = 0; policy_id < MAX_POLICIES; policy_id++) {
        policy = bpf_map_lookup_elem(&policy_map, &policy_id);
        if (!policy || !policy->enabled) continue;

        if (evaluate_policy(policy, args, argc)) {
            action = 1; // Block

            // Update statistics and cache
            update_stats(1);
            update_cache(args_hash, 1, current_time);

            // Send event to user space
            event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
            if (event) {
                event->pid = pid;
                event->ppid = BPF_CORE_READ(task, real_parent, pid);
                event->uid = uid;
                event->gid = gid;
                event->argc = argc;
                event->action = action;
                event->timestamp = current_time;

                bpf_get_current_comm(event->comm, sizeof(event->comm));
                bpf_safe_strcpy(event->filename, filename, sizeof(event->filename));
                bpf_safe_strcpy(event->policy_matched, policy->name, sizeof(event->policy_matched));

                bpf_ringbuf_submit(event, 0);
            }

            return -1; // Block execution
        }
    }

    // Update statistics and cache for allowed execution
    update_stats(0);
    update_cache(args_hash, 0, current_time);
    
    return 0; // Allow execution
}

char _license[] SEC("license") = "GPL";
