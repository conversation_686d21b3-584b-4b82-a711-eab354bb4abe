/*
 * eBPF Process Control Tool - Simple Optimized Version Header
 * Shared structures and constants for optimized eBPF implementation
 */

#ifndef ARGS_ENFORCER_SIMPLE_H
#define ARGS_ENFORCER_SIMPLE_H

/* Use basic types to avoid conflicts with vmlinux.h */
#ifndef __u32
typedef unsigned int __u32;
#endif
#ifndef __u64
typedef unsigned long long __u64;
#endif

/* Optimized constants */
#define MAX_ARGS 5  // Limited to first 5 arguments for performance
#define MAX_ARG_LEN 64  // Reduced size since no spaces in individual args
#define MAX_EXECUTABLE_PATH_LEN 256
#define MAX_POLICY_ARGS_STR_LEN 512  // Space-separated args string
#define MAX_POLICIES 100

/* Event structure for user-space communication */
struct exec_event {
    __u32 pid;
    __u32 ppid;
    __u32 uid;
    __u32 gid;
    char comm[16];
    char filename[MAX_EXECUTABLE_PATH_LEN];
    char args[MAX_ARGS][MAX_ARG_LEN];
    int argc;
    int action; // 0 = allow, 1 = deny
    char policy_matched[64];
    __u64 timestamp;
};

/* Statistics structure */
struct policy_stats {
    __u64 total_executions;
    __u64 blocked_executions;
    __u64 allowed_executions;
    __u64 policy_lookups;
    __u64 policy_matches;
};

#endif /* ARGS_ENFORCER_SIMPLE_H */
