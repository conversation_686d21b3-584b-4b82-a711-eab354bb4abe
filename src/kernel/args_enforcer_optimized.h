/*
 * eBPF Process Control Tool - Optimized Version Header
 * Shared structures and constants for optimized eBPF implementation
 */

#ifndef ARGS_ENFORCER_OPTIMIZED_H
#define ARGS_ENFORCER_OPTIMIZED_H

#include <linux/types.h>

/* Optimized constants */
#define MAX_ARGS 5  // Reduced from 64 to 5 for performance
#define MAX_ARG_LEN 256
#define MAX_EXECUTABLE_PATH_LEN 256
#define MAX_POLICY_ARGS 16  // Reduced from 32
#define MAX_POLICY_NAME_LEN 64
#define MAX_POLICIES 100

/* Executable-based policy structure */
struct executable_policy {
    char executable_path[MAX_EXECUTABLE_PATH_LEN];
    char policy_name[MAX_POLICY_NAME_LEN];
    char include_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int include_count;
    int exclude_count;
    int enabled;
    __u64 last_updated;
    __u32 match_count;
};

/* Event structure for user-space communication */
struct exec_event {
    __u32 pid;
    __u32 ppid;
    __u32 uid;
    __u32 gid;
    char comm[16];
    char filename[MAX_EXECUTABLE_PATH_LEN];
    char args[MAX_ARGS][MAX_ARG_LEN];
    int argc;
    int action; // 0 = allow, 1 = deny
    char policy_matched[MAX_POLICY_NAME_LEN];
    __u64 timestamp;
};

/* Statistics structure */
struct policy_stats {
    __u64 total_executions;
    __u64 blocked_executions;
    __u64 allowed_executions;
    __u64 policy_lookups;
    __u64 policy_matches;
};

/* Configuration structure for user-space */
struct optimized_config {
    int max_policies;
    int max_args;
    int max_arg_len;
    int verbose_logging;
    char log_file[256];
};

#endif /* ARGS_ENFORCER_OPTIMIZED_H */
