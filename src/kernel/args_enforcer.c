// SPDX-License-Identifier: GPL-2.0
/*
 * eBPF Process Control Tool - Kernel Space
 * Command-line argument policy enforcement in eBPF
 */

#include "vmlinux.h"
#include <bpf/bpf_helpers.h>
#include <bpf/bpf_tracing.h>
#include <bpf/bpf_core_read.h>

#define MAX_ARGS 5  // Reduced from 64 to 5 for performance optimization
#define MAX_ARG_LEN 256
#define MAX_POLICIES 100
#define MAX_POLICY_ARGS 32
#define MAX_POLICY_NAME_LEN 64

/* Policy structure for eBPF maps */
struct policy_rule {
    char name[MAX_POLICY_NAME_LEN];
    char include_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int include_count;
    int exclude_count;
    int enabled;
};

/* Event structure for user-space communication */
struct exec_event {
    __u32 pid;
    __u32 ppid;
    __u32 uid;
    __u32 gid;
    char comm[16];
    char filename[256];
    char args[MAX_ARGS][MAX_ARG_LEN];
    int argc;
    int action; // 0 = allow, 1 = deny
    char policy_matched[MAX_POLICY_NAME_LEN];
};

/* BPF Maps */
struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, MAX_POLICIES);
    __type(key, __u32);
    __type(value, struct policy_rule);
} policy_map SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_RINGBUF);
    __uint(max_entries, 256 * 1024);
} events SEC(".maps");

struct {
    __uint(type, BPF_MAP_TYPE_HASH);
    __uint(max_entries, 1024);
    __type(key, __u32);
    __type(value, __u32);
} blocked_pids SEC(".maps");

/* Statistics structure */
struct stats {
    __u64 total_execve_calls;
    __u64 blocked_executions;
    __u64 allowed_executions;
    __u64 policy_evaluations;
    __u64 cache_hits;
    __u64 cache_misses;
};

struct {
    __uint(type, BPF_MAP_TYPE_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, struct stats);
} stats_map SEC(".maps");

/* Cache for recent policy decisions */
struct cache_entry {
    __u32 hash;
    __u32 decision;  // 0 = allow, 1 = block
    __u64 timestamp;
};

struct {
    __uint(type, BPF_MAP_TYPE_LRU_HASH);
    __uint(max_entries, 1024);
    __type(key, __u32);
    __type(value, struct cache_entry);
} decision_cache SEC(".maps");

/* Rate limiting map */
struct {
    __uint(type, BPF_MAP_TYPE_LRU_HASH);
    __uint(max_entries, 1024);
    __type(key, __u32);  // PID
    __type(value, __u64); // Last execution time
} rate_limit_map SEC(".maps");

/* Per-CPU array for temporary argument storage */
struct {
    __uint(type, BPF_MAP_TYPE_PERCPU_ARRAY);
    __uint(max_entries, 1);
    __type(key, __u32);
    __type(value, char[MAX_ARGS][MAX_ARG_LEN]);
} temp_args_map SEC(".maps");

/* Helper function to compare strings */
static __always_inline int bpf_strcmp(const char *s1, const char *s2, int max_len) {
    for (int i = 0; i < max_len; i++) {
        if (s1[i] != s2[i])
            return s1[i] - s2[i];
        if (s1[i] == '\0')
            return 0;
    }
    return 0;
}

/* Helper function to get string length */
static __always_inline int bpf_strlen(const char *str, int max_len) {
    for (int i = 0; i < max_len; i++) {
        if (str[i] == '\0')
            return i;
    }
    return max_len;
}

/* Helper function to find substring in string */
static __always_inline int bpf_strstr(const char *haystack, const char *needle, int haystack_len, int needle_len) {
    if (needle_len == 0) return 1;
    if (needle_len > haystack_len) return 0;

    for (int i = 0; i <= haystack_len - needle_len; i++) {
        int match = 1;
        for (int j = 0; j < needle_len; j++) {
            if (haystack[i + j] != needle[j]) {
                match = 0;
                break;
            }
        }
        if (match) return 1;
    }
    return 0;
}

/* Simple wildcard matching (supports * at end of pattern) */
static __always_inline int match_wildcard(const char *arg, const char *pattern) {
    int arg_len = bpf_strlen(arg, MAX_ARG_LEN);
    int pattern_len = bpf_strlen(pattern, MAX_ARG_LEN);

    if (pattern_len == 0) return 0;

    // Check if pattern ends with '*'
    if (pattern_len > 0 && pattern[pattern_len - 1] == '*') {
        // Match prefix
        int prefix_len = pattern_len - 1;
        if (arg_len >= prefix_len) {
            for (int i = 0; i < prefix_len; i++) {
                if (arg[i] != pattern[i]) {
                    return 0;
                }
            }
            return 1;
        }
        return 0;
    }

    // Exact match if no wildcard
    return (arg_len == pattern_len && bpf_strcmp(arg, pattern, pattern_len) == 0);
}

/* Enhanced argument matching with exact, partial, and wildcard matching */
static __always_inline int match_argument(const char *arg, const char *pattern) {
    int arg_len = bpf_strlen(arg, MAX_ARG_LEN);
    int pattern_len = bpf_strlen(pattern, MAX_ARG_LEN);

    if (pattern_len == 0) return 0;

    // Wildcard matching
    if (match_wildcard(arg, pattern)) {
        return 1;
    }

    // Exact match
    if (arg_len == pattern_len && bpf_strcmp(arg, pattern, pattern_len) == 0) {
        return 1;
    }

    // Substring match for patterns like "-o StrictHostKeyChecking=yes"
    if (bpf_strstr(arg, pattern, arg_len, pattern_len)) {
        return 1;
    }

    return 0;
}

/* Check if argument exists in argument list with enhanced matching */
static __always_inline int check_arg_exists(char args[][MAX_ARG_LEN], int argc, const char *target_arg) {
    for (int i = 0; i < argc && i < MAX_ARGS; i++) {
        if (match_argument(args[i], target_arg)) {
            return 1;
        }
    }
    return 0;
}

/* Check for argument combinations (e.g., "-o" followed by "StrictHostKeyChecking=no") */
static __always_inline int check_arg_combination(char args[][MAX_ARG_LEN], int argc, const char *target_arg) {
    int target_len = bpf_strlen(target_arg, MAX_ARG_LEN);

    // Check for single argument match first
    if (check_arg_exists(args, argc, target_arg)) {
        return 1;
    }

    // Check for split arguments (e.g., "-o" "StrictHostKeyChecking=no")
    for (int i = 0; i < argc - 1 && i < MAX_ARGS - 1; i++) {
        // Look for patterns like "-o StrictHostKeyChecking=no"
        if (bpf_strcmp(args[i], "-o", 3) == 0 && i + 1 < argc) {
            if (match_argument(args[i + 1], target_arg + 3)) { // Skip "-o "
                return 1;
            }
        }
    }

    return 0;
}

/* Evaluate policy against arguments with enhanced logic */
static __always_inline int evaluate_policy(struct policy_rule *policy, char args[][MAX_ARG_LEN], int argc) {
    // Check include rules (AND logic - all must be present)
    for (int i = 0; i < policy->include_count && i < MAX_POLICY_ARGS; i++) {
        if (policy->include_args[i][0] != '\0') {
            if (!check_arg_combination(args, argc, policy->include_args[i])) {
                return 1; // Block - required argument missing
            }
        }
    }

    // Check exclude rules (OR logic - any present blocks execution)
    for (int i = 0; i < policy->exclude_count && i < MAX_POLICY_ARGS; i++) {
        if (policy->exclude_args[i][0] != '\0') {
            if (check_arg_combination(args, argc, policy->exclude_args[i])) {
                return 1; // Block - forbidden argument present
            }
        }
    }

    return 0; // Allow
}

/* Parse command line arguments from execve with enhanced error handling */
static __always_inline int parse_args(struct pt_regs *ctx, char args[][MAX_ARG_LEN]) {
    const char *const *argv;
    const char *arg;
    int argc = 0;
    long ret;

    argv = (const char *const *)PT_REGS_PARM2(ctx);
    if (!argv)
        return 0;

    // Initialize args array
    for (int i = 0; i < MAX_ARGS; i++) {
        args[i][0] = '\0';
    }

    for (int i = 0; i < MAX_ARGS; i++) {
        // Read argv[i] pointer
        ret = bpf_probe_read_user(&arg, sizeof(arg), &argv[i]);
        if (ret != 0 || !arg)
            break;

        // Read the actual argument string
        ret = bpf_probe_read_user_str(args[i], MAX_ARG_LEN, arg);
        if (ret < 0)
            break;

        // Ensure null termination
        args[i][MAX_ARG_LEN - 1] = '\0';
        argc++;
    }

    return argc;
}

/* Simple hash function for argument arrays */
static __always_inline __u32 hash_args(char args[][MAX_ARG_LEN], int argc) {
    __u32 hash = 5381;

    for (int i = 0; i < argc && i < MAX_ARGS; i++) {
        for (int j = 0; j < MAX_ARG_LEN && args[i][j] != '\0'; j++) {
            hash = ((hash << 5) + hash) + args[i][j];
        }
    }

    return hash;
}

/* Check decision cache */
static __always_inline int check_cache(__u32 hash, __u64 current_time) {
    struct cache_entry *entry;
    __u32 key = 0;
    struct stats *stats_ptr;

    entry = bpf_map_lookup_elem(&decision_cache, &hash);
    if (entry && (current_time - entry->timestamp) < 60000000000ULL) { // 60 seconds cache
        // Update cache hit statistics
        stats_ptr = bpf_map_lookup_elem(&stats_map, &key);
        if (stats_ptr) {
            __sync_fetch_and_add(&stats_ptr->cache_hits, 1);
        }
        return entry->decision;
    }

    // Update cache miss statistics
    stats_ptr = bpf_map_lookup_elem(&stats_map, &key);
    if (stats_ptr) {
        __sync_fetch_and_add(&stats_ptr->cache_misses, 1);
    }

    return -1; // Cache miss
}

/* Update decision cache */
static __always_inline void update_cache(__u32 hash, int decision, __u64 timestamp) {
    struct cache_entry entry = {
        .hash = hash,
        .decision = decision,
        .timestamp = timestamp
    };

    bpf_map_update_elem(&decision_cache, &hash, &entry, BPF_ANY);
}

/* Rate limiting check */
static __always_inline int check_rate_limit(__u32 pid, __u64 current_time) {
    __u64 *last_time;

    last_time = bpf_map_lookup_elem(&rate_limit_map, &pid);
    if (last_time && (current_time - *last_time) < 1000000000ULL) { // 1 second rate limit
        return 1; // Rate limited
    }

    bpf_map_update_elem(&rate_limit_map, &pid, &current_time, BPF_ANY);
    return 0; // Not rate limited
}

/* Update statistics */
static __always_inline void update_stats(int blocked) {
    __u32 key = 0;
    struct stats *stats_ptr;

    stats_ptr = bpf_map_lookup_elem(&stats_map, &key);
    if (stats_ptr) {
        __sync_fetch_and_add(&stats_ptr->total_execve_calls, 1);
        if (blocked) {
            __sync_fetch_and_add(&stats_ptr->blocked_executions, 1);
        } else {
            __sync_fetch_and_add(&stats_ptr->allowed_executions, 1);
        }
    }
}

SEC("tracepoint/syscalls/sys_enter_execve")
int trace_execve(struct trace_event_raw_sys_enter *ctx) {
    struct exec_event *event;
    struct policy_rule *policy;
    struct task_struct *task;
    __u32 pid, uid, gid;
    int argc, action = 0;
    char args[MAX_ARGS][MAX_ARG_LEN];
    char filename[256];
    __u64 current_time;
    __u32 args_hash;
    int cached_decision;
    const char *filename_ptr;

    // Get current time
    current_time = bpf_ktime_get_ns();

    // Get current task info
    task = (struct task_struct *)bpf_get_current_task();
    pid = bpf_get_current_pid_tgid() >> 32;
    uid = bpf_get_current_uid_gid() & 0xffffffff;
    gid = bpf_get_current_uid_gid() >> 32;

    // Rate limiting check
    if (check_rate_limit(pid, current_time)) {
        return 0; // Allow but rate limited
    }
    
    // Parse filename
    filename_ptr = (const char *)ctx->args[0];
    if (bpf_probe_read_user_str(filename, sizeof(filename), filename_ptr) < 0)
        return 0;
    
    // Parse arguments
    argc = parse_args((struct pt_regs *)ctx, args);
    if (argc <= 0)
        return 0;

    // Calculate hash for caching
    args_hash = hash_args(args, argc);

    // Check cache first
    cached_decision = check_cache(args_hash, current_time);
    if (cached_decision >= 0) {
        if (cached_decision == 1) {
            update_stats(1);
            return -1; // Block based on cache
        } else {
            update_stats(0);
            return 0; // Allow based on cache
        }
    }

    // Check policies
    for (__u32 policy_id = 0; policy_id < MAX_POLICIES; policy_id++) {
        policy = bpf_map_lookup_elem(&policy_map, &policy_id);
        if (!policy || !policy->enabled)
            continue;

        // Update policy evaluation counter
        __u32 stats_key = 0;
        struct stats *stats_ptr = bpf_map_lookup_elem(&stats_map, &stats_key);
        if (stats_ptr) {
            __sync_fetch_and_add(&stats_ptr->policy_evaluations, 1);
        }

        if (evaluate_policy(policy, args, argc)) {
            action = 1; // Block

            // Update statistics
            update_stats(1);

            // Update cache with block decision
            update_cache(args_hash, 1, current_time);

            // Add to blocked PIDs map
            __u32 blocked = 1;
            bpf_map_update_elem(&blocked_pids, &pid, &blocked, BPF_ANY);

            // Send event to user space
            event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
            if (event) {
                event->pid = pid;
                event->ppid = BPF_CORE_READ(task, real_parent, pid);
                event->uid = uid;
                event->gid = gid;
                event->argc = argc;
                event->action = action;

                bpf_get_current_comm(event->comm, sizeof(event->comm));
                bpf_probe_read_kernel_str(event->filename, sizeof(event->filename), filename);
                bpf_probe_read_kernel_str(event->policy_matched, sizeof(event->policy_matched), policy->name);

                // Copy arguments
                for (int i = 0; i < argc && i < MAX_ARGS; i++) {
                    bpf_probe_read_kernel_str(event->args[i], MAX_ARG_LEN, args[i]);
                }

                bpf_ringbuf_submit(event, 0);
            }

            return -1; // Block execution
        }
    }

    // Update statistics for allowed execution
    update_stats(0);

    // Update cache with allow decision
    update_cache(args_hash, 0, current_time);

    // Send allow event to user space for logging
    event = bpf_ringbuf_reserve(&events, sizeof(*event), 0);
    if (event) {
        event->pid = pid;
        event->ppid = BPF_CORE_READ(task, real_parent, pid);
        event->uid = uid;
        event->gid = gid;
        event->argc = argc;
        event->action = 0; // Allow
        
        bpf_get_current_comm(event->comm, sizeof(event->comm));
        bpf_probe_read_kernel_str(event->filename, sizeof(event->filename), filename);
        event->policy_matched[0] = '\0';
        
        // Copy arguments
        for (int i = 0; i < argc && i < MAX_ARGS; i++) {
            bpf_probe_read_kernel_str(event->args[i], MAX_ARG_LEN, args[i]);
        }
        
        bpf_ringbuf_submit(event, 0);
    }
    
    return 0; // Allow execution
}

char _license[] SEC("license") = "GPL";
