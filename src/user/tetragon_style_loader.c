/*
 * Tetragon-style eBPF Program Loader
 * Loads and manages the improved eBPF process control program
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <signal.h>
#include <sys/resource.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include "string_utils.h"

#define MAX_POLICIES 16
#define MAX_POLICY_ARGS 8
#define MAX_POLICY_NAME_LEN 32
#define MAX_ARG_LEN 128

/* Policy structure matching kernel */
struct simple_policy {
    char name[MAX_POLICY_NAME_LEN];
    char exclude_args[MAX_POLICY_ARGS][MAX_ARG_LEN];
    int exclude_count;
    int enabled;
};

/* Event structure matching kernel */
struct simple_event {
    __u32 pid;
    __u32 uid;
    char comm[16];
    char filename[64];
    int action; // 0 = allow, 1 = deny
    char policy_matched[MAX_POLICY_NAME_LEN];
    __u64 timestamp;
};

static volatile bool running = true;
static struct bpf_object *obj = NULL;
static struct bpf_link *bpf_link = NULL;
static int events_fd = -1;

/* Signal handler */
static void sig_handler(int sig) {
    (void)sig;
    running = false;
}

/* Event handler */
static int handle_event(void *ctx, void *data, size_t data_sz) {
    (void)ctx;
    (void)data_sz;
    
    struct simple_event *event = (struct simple_event *)data;
    
    // Format timestamp
    char timestamp_str[64];
    format_timestamp(event->timestamp, timestamp_str, sizeof(timestamp_str));
    
    // Extract filename
    const char *filename = extract_filename(event->filename);
    
    if (event->action == 1) {
        printf("[%s] 🚫 BLOCKED: PID=%d UID=%d CMD=%s POLICY=%s\n", 
               timestamp_str, event->pid, event->uid, filename, event->policy_matched);
        printf("  Process: %s\n", event->comm);
        printf("  Full path: %s\n", event->filename);
    } else {
        printf("[%s] ✅ ALLOWED: PID=%d UID=%d CMD=%s\n", 
               timestamp_str, event->pid, event->uid, filename);
    }
    
    return 0;
}

/* Load default policies */
static int load_default_policies(int policy_map_fd) {
    struct simple_policy policies[] = {
        {
            .name = "ssh_security",
            .exclude_args = {
                "StrictHostKeyChecking=no",
                "UserKnownHostsFile=/dev/null",
                "-o StrictHostKeyChecking=no"
            },
            .exclude_count = 3,
            .enabled = 1
        },
        {
            .name = "curl_security", 
            .exclude_args = {
                "-k",
                "--insecure"
            },
            .exclude_count = 2,
            .enabled = 1
        },
        {
            .name = "docker_security",
            .exclude_args = {
                "--privileged",
                "--cap-add=ALL"
            },
            .exclude_count = 2,
            .enabled = 1
        },
        {
            .name = "unsafe_patterns",
            .exclude_args = {
                "--unsafe-",
                "--debug-"
            },
            .exclude_count = 2,
            .enabled = 1
        }
    };
    
    for (int i = 0; i < 4; i++) {
        __u32 key = i;
        if (bpf_map_update_elem(policy_map_fd, &key, &policies[i], BPF_ANY) != 0) {
            fprintf(stderr, "Failed to update policy %d: %s\n", i, strerror(errno));
            return -1;
        }
        printf("✓ Loaded policy: %s\n", policies[i].name);
    }
    
    return 0;
}

/* Load eBPF program */
static int load_bpf_program(void) {
    struct bpf_program *prog, *stage2_prog, *stage3_prog;
    int policy_map_fd, tail_calls_fd;
    int err;

    /* Increase RLIMIT_MEMLOCK to allow BPF */
    struct rlimit rlim_new = {
        .rlim_cur = RLIM_INFINITY,
        .rlim_max = RLIM_INFINITY,
    };

    if (setrlimit(RLIMIT_MEMLOCK, &rlim_new)) {
        fprintf(stderr, "Failed to increase RLIMIT_MEMLOCK: %s\n", strerror(errno));
        return -1;
    }

    /* Load BPF object */
    obj = bpf_object__open("build/tetragon_style_enforcer.o");
    if (libbpf_get_error(obj)) {
        fprintf(stderr, "Failed to open BPF object: %s\n",
                strerror(-libbpf_get_error(obj)));
        return -1;
    }

    /* Load BPF program into kernel */
    err = bpf_object__load(obj);
    if (err) {
        fprintf(stderr, "Failed to load BPF object: %s\n", strerror(-err));
        bpf_object__close(obj);
        return -1;
    }

    /* Find all programs */
    prog = bpf_object__find_program_by_name(obj, "tetragon_style_execve");
    if (!prog) {
        fprintf(stderr, "Failed to find main BPF program\n");
        bpf_object__close(obj);
        return -1;
    }

    stage2_prog = bpf_object__find_program_by_name(obj, "stage2_process_args");
    if (!stage2_prog) {
        fprintf(stderr, "Failed to find stage2 BPF program\n");
        bpf_object__close(obj);
        return -1;
    }

    stage3_prog = bpf_object__find_program_by_name(obj, "stage3_policy_check");
    if (!stage3_prog) {
        fprintf(stderr, "Failed to find stage3 BPF program\n");
        bpf_object__close(obj);
        return -1;
    }

    /* Get tail calls map FD */
    tail_calls_fd = bpf_object__find_map_fd_by_name(obj, "tail_calls");
    if (tail_calls_fd < 0) {
        fprintf(stderr, "Failed to find tail_calls map\n");
        goto cleanup;
    }

    /* Set up tail call entries */
    __u32 key = 1;
    int stage2_fd = bpf_program__fd(stage2_prog);
    if (bpf_map_update_elem(tail_calls_fd, &key, &stage2_fd, BPF_ANY) != 0) {
        fprintf(stderr, "Failed to update tail_calls map for stage2: %s\n", strerror(errno));
        goto cleanup;
    }

    key = 2;
    int stage3_fd = bpf_program__fd(stage3_prog);
    if (bpf_map_update_elem(tail_calls_fd, &key, &stage3_fd, BPF_ANY) != 0) {
        fprintf(stderr, "Failed to update tail_calls map for stage3: %s\n", strerror(errno));
        goto cleanup;
    }

    /* Attach to tracepoint */
    bpf_link = bpf_program__attach(prog);
    if (libbpf_get_error(bpf_link)) {
        fprintf(stderr, "Failed to attach BPF program: %s\n",
                strerror(-libbpf_get_error(bpf_link)));
        bpf_object__close(obj);
        return -1;
    }

    /* Get policy map FD */
    policy_map_fd = bpf_object__find_map_fd_by_name(obj, "policy_map");
    if (policy_map_fd < 0) {
        fprintf(stderr, "Failed to find policy map\n");
        goto cleanup;
    }

    /* Load default policies */
    if (load_default_policies(policy_map_fd) != 0) {
        goto cleanup;
    }

    /* Get events map FD */
    events_fd = bpf_object__find_map_fd_by_name(obj, "events");
    if (events_fd < 0) {
        fprintf(stderr, "Failed to find events map\n");
        goto cleanup;
    }

    printf("✓ eBPF program loaded and attached successfully\n");
    return 0;

cleanup:
    if (bpf_link) {
        bpf_link__destroy(bpf_link);
        bpf_link = NULL;
    }
    if (obj) {
        bpf_object__close(obj);
        obj = NULL;
    }
    return -1;
}

/* Cleanup resources */
static void cleanup(void) {
    if (bpf_link) {
        bpf_link__destroy(bpf_link);
        bpf_link = NULL;
    }
    if (obj) {
        bpf_object__close(obj);
        obj = NULL;
    }
}

int main(int argc, char **argv) {
    (void)argc; // Suppress unused parameter warning
    (void)argv; // Suppress unused parameter warning

    struct ring_buffer *rb = NULL;
    int err;

    printf("=== Tetragon-Style eBPF Process Control Tool ===\n");
    printf("Loading improved eBPF program with FIM-inspired string processing...\n\n");

    /* Set up signal handlers */
    signal(SIGINT, sig_handler);
    signal(SIGTERM, sig_handler);

    /* Load BPF program */
    if (load_bpf_program() != 0) {
        return 1;
    }

    printf("\n🚀 Process monitoring started. Press Ctrl+C to stop.\n");
    printf("Monitoring for policy violations...\n\n");

    /* Set up ring buffer polling */
    rb = ring_buffer__new(events_fd, handle_event, NULL, NULL);
    if (!rb) {
        fprintf(stderr, "Failed to create ring buffer\n");
        cleanup();
        return 1;
    }

    /* Main event loop */
    while (running) {
        err = ring_buffer__poll(rb, 100); /* 100ms timeout */
        if (err < 0 && err != -EINTR) {
            fprintf(stderr, "Error polling ring buffer: %s\n", strerror(-err));
            break;
        }
    }

    printf("\n🛑 Shutting down...\n");

    /* Cleanup */
    if (rb) {
        ring_buffer__free(rb);
    }
    cleanup();

    printf("✓ Cleanup completed\n");
    return 0;
}
