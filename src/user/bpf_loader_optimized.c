/*
 * Optimized eBPF Program Loader
 * Loads and manages the optimized eBPF process control program
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <sys/resource.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include "../kernel/args_enforcer_optimized.h"

#define BPF_PROGRAM_PATH "src/kernel/args_enforcer_optimized.o"

/* Simple logging functions */
#define log_info(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define log_error(fmt, ...) fprintf(stderr, "[ERROR] " fmt "\n", ##__VA_ARGS__)
#define log_warning(fmt, ...) printf("[WARNING] " fmt "\n", ##__VA_ARGS__)
#define log_debug(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)

/* Load optimized eBPF program from object file */
int bpf_load_optimized_enforcer_program(struct bpf_object **obj, int *events_fd) {
    struct bpf_program *prog;
    struct bpf_map *events_map;
    int err;
    
    log_info("Loading optimized eBPF program from %s", BPF_PROGRAM_PATH);
    
    // Check if object file exists
    if (access(BPF_PROGRAM_PATH, R_OK) != 0) {
        log_error("eBPF object file not found: %s", BPF_PROGRAM_PATH);
        log_error("Please run 'make' to build the optimized eBPF program");
        return -1;
    }
    
    // Open eBPF object file
    *obj = bpf_object__open(BPF_PROGRAM_PATH);
    if (libbpf_get_error(*obj)) {
        log_error("Failed to open eBPF object file: %s", strerror(errno));
        return -1;
    }
    
    // Load eBPF program into kernel
    err = bpf_object__load(*obj);
    if (err) {
        log_error("Failed to load eBPF program: %s", strerror(-err));
        bpf_object__close(*obj);
        return -1;
    }
    
    // Find the events map
    events_map = bpf_object__find_map_by_name(*obj, "events");
    if (!events_map) {
        log_error("Failed to find events map in eBPF object");
        bpf_object__close(*obj);
        return -1;
    }
    
    *events_fd = bpf_map__fd(events_map);
    if (*events_fd < 0) {
        log_error("Failed to get events map file descriptor");
        bpf_object__close(*obj);
        return -1;
    }
    
    log_info("Successfully loaded optimized eBPF program");
    return 0;
}

/* Attach optimized eBPF program to tracepoint */
int bpf_attach_optimized_program(struct bpf_object *obj) {
    struct bpf_program *prog;
    struct bpf_link *link;
    int err;
    
    log_info("Attaching optimized eBPF program to tracepoint");
    
    // Find the main program
    prog = bpf_object__find_program_by_name(obj, "trace_execve");
    if (!prog) {
        log_error("Failed to find trace_execve program in eBPF object");
        return -1;
    }
    
    // Attach to tracepoint
    link = bpf_program__attach(prog);
    if (libbpf_get_error(link)) {
        log_error("Failed to attach eBPF program: %s", strerror(errno));
        return -1;
    }
    
    log_info("Successfully attached optimized eBPF program");
    return 0;
}

/* Get map file descriptor by name */
int bpf_get_optimized_map_fd(struct bpf_object *obj, const char *map_name) {
    struct bpf_map *map;
    int fd;
    
    map = bpf_object__find_map_by_name(obj, map_name);
    if (!map) {
        log_error("Failed to find map '%s' in eBPF object", map_name);
        return -1;
    }
    
    fd = bpf_map__fd(map);
    if (fd < 0) {
        log_error("Failed to get file descriptor for map '%s'", map_name);
        return -1;
    }
    
    return fd;
}

/* Update executable policy in map */
int bpf_update_executable_policy(struct bpf_object *obj, const char *executable_path, 
                                struct executable_policy *policy) {
    int map_fd;
    int err;
    
    map_fd = bpf_get_optimized_map_fd(obj, "executable_policy_map");
    if (map_fd < 0) {
        return -1;
    }
    
    err = bpf_map_update_elem(map_fd, executable_path, policy, BPF_ANY);
    if (err) {
        log_error("Failed to update policy for executable '%s': %s", 
                 executable_path, strerror(errno));
        return -1;
    }
    
    log_debug("Updated policy for executable '%s'", executable_path);
    return 0;
}

/* Clear all policies from executable policy map */
int bpf_clear_executable_policy_map(struct bpf_object *obj) {
    int map_fd;
    char executable_path[MAX_EXECUTABLE_PATH_LEN];
    struct executable_policy policy;
    int err = 0;
    
    map_fd = bpf_get_optimized_map_fd(obj, "executable_policy_map");
    if (map_fd < 0) {
        return -1;
    }
    
    // For hash maps, we need to iterate through existing keys to delete them
    // This is a simplified approach - in practice, you might want to track keys
    log_info("Clearing executable policy map");
    
    // Note: BPF hash maps don't have a direct "clear all" operation
    // We would need to iterate through existing keys, but for simplicity
    // we'll just overwrite with new policies in the policy loader
    
    return 0;
}

/* Get statistics from eBPF program */
int bpf_get_optimized_stats(struct bpf_object *obj, struct policy_stats *stats) {
    int map_fd;
    __u32 key = 0;
    __u64 value;
    
    if (!stats) {
        return -1;
    }
    
    map_fd = bpf_get_optimized_map_fd(obj, "stats_map");
    if (map_fd < 0) {
        return -1;
    }
    
    // Read statistics from map
    if (bpf_map_lookup_elem(map_fd, &key, &value) == 0) {
        // For simplicity, we're just reading one stat value
        // In a real implementation, you'd have separate keys for different stats
        stats->total_executions = value;
        stats->blocked_executions = 0;  // Would need separate map entries
        stats->allowed_executions = 0;
        stats->policy_lookups = 0;
        stats->policy_matches = 0;
    } else {
        memset(stats, 0, sizeof(*stats));
    }
    
    return 0;
}

/* Cleanup optimized eBPF program */
void bpf_cleanup_optimized_program(struct bpf_object *obj) {
    if (obj) {
        log_info("Cleaning up optimized eBPF program");
        bpf_object__close(obj);
    }
}

/* Set up resource limits for eBPF */
int bpf_setup_rlimits(void) {
    struct rlimit rlim = {
        .rlim_cur = RLIM_INFINITY,
        .rlim_max = RLIM_INFINITY,
    };
    
    if (setrlimit(RLIMIT_MEMLOCK, &rlim)) {
        log_error("Failed to increase RLIMIT_MEMLOCK: %s", strerror(errno));
        return -1;
    }
    
    log_debug("Successfully set up resource limits for eBPF");
    return 0;
}

/* Print optimized program information */
void bpf_print_optimized_info(struct bpf_object *obj) {
    struct bpf_program *prog;
    struct bpf_map *map;
    
    printf("\nOptimized eBPF Program Information:\n");
    printf("===================================\n");
    
    printf("Programs:\n");
    bpf_object__for_each_program(prog, obj) {
        printf("  - %s (type: %d)\n", 
               bpf_program__name(prog), 
               bpf_program__type(prog));
    }
    
    printf("\nMaps:\n");
    bpf_object__for_each_map(map, obj) {
        printf("  - %s (type: %d, max_entries: %d)\n",
               bpf_map__name(map),
               bpf_map__type(map),
               bpf_map__max_entries(map));
    }
    
    printf("\nOptimizations:\n");
    printf("  - Limited to first %d command-line arguments\n", MAX_ARGS);
    printf("  - Executable-based policy storage (O(1) lookup)\n");
    printf("  - Reduced policy structure size\n");
    printf("  - Direct hash map lookup instead of iteration\n");
    printf("\n");
}
