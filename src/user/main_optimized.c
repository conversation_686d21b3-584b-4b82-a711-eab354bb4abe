/*
 * Optimized eBPF Process Control Tool - Main Program
 * Features:
 * 1. Limited to first 5 command-line arguments for performance
 * 2. Executable-based policy storage for O(1) lookup
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <getopt.h>
#include <sys/resource.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include "../kernel/args_enforcer_optimized.h"

/* External function declarations */
extern int policy_load_optimized_config(const char *config_path);
extern int policy_apply_to_optimized_bpf(struct bpf_object *obj);
extern void policy_list_optimized(void);
extern void policy_optimized_cleanup(void);
extern int policy_get_optimized_count(void);

extern int bpf_load_optimized_enforcer_program(struct bpf_object **obj, int *events_fd);
extern int bpf_attach_optimized_program(struct bpf_object *obj);
extern void bpf_cleanup_optimized_program(struct bpf_object *obj);
extern int bpf_setup_rlimits(void);
extern void bpf_print_optimized_info(struct bpf_object *obj);
extern int bpf_get_optimized_stats(struct bpf_object *obj, struct policy_stats *stats);

/* Simple logging functions */
#define log_info(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define log_error(fmt, ...) fprintf(stderr, "[ERROR] " fmt "\n", ##__VA_ARGS__)
#define log_warning(fmt, ...) printf("[WARNING] " fmt "\n", ##__VA_ARGS__)

/* Global variables */
static struct bpf_object *obj = NULL;
static int events_fd = -1;
static volatile int running = 1;

/* Signal handler */
static void signal_handler(int sig) {
    log_info("Received signal %d, shutting down...", sig);
    running = 0;
}

/* Handle events from eBPF program */
static int handle_event(void *ctx, void *data, size_t data_sz) {
    struct exec_event *event = (struct exec_event *)data;
    
    if (data_sz < sizeof(*event)) {
        log_error("Invalid event size: %zu", data_sz);
        return 0;
    }
    
    printf("[%s] PID: %u, UID: %u, Executable: %s, Policy: %s\n",
           event->action ? "BLOCKED" : "ALLOWED",
           event->pid,
           event->uid,
           event->filename,
           event->policy_matched[0] ? event->policy_matched : "none");
    
    if (event->action) {
        printf("  Command: %s", event->comm);
        for (int i = 0; i < event->argc && i < MAX_ARGS; i++) {
            printf(" %s", event->args[i]);
        }
        printf("\n");
    }
    
    return 0;
}

/* Main event loop */
static int run_event_loop(void) {
    struct ring_buffer *rb;
    int err;
    
    log_info("Starting optimized event loop");
    
    // Create ring buffer for events
    rb = ring_buffer__new(events_fd, handle_event, NULL, NULL);
    if (!rb) {
        log_error("Failed to create ring buffer");
        return -1;
    }
    
    // Main event loop
    while (running) {
        err = ring_buffer__poll(rb, 100); // 100ms timeout
        if (err == -EINTR) {
            break;
        }
        if (err < 0) {
            log_error("Error polling ring buffer: %d", err);
            break;
        }
    }
    
    ring_buffer__free(rb);
    log_info("Event loop terminated");
    return 0;
}

/* Print usage information */
static void print_usage(const char *prog_name) {
    printf("Usage: %s [OPTIONS]\n", prog_name);
    printf("\nOptimized eBPF Process Control Tool\n");
    printf("Features: 5-arg limit, executable-based policies, O(1) lookup\n\n");
    printf("Options:\n");
    printf("  -c, --config FILE    Configuration file (default: configs/optimized_policy.json)\n");
    printf("  -l, --list          List loaded policies and exit\n");
    printf("  -s, --stats         Show statistics and exit\n");
    printf("  -i, --info          Show program information and exit\n");
    printf("  -v, --verbose       Enable verbose logging\n");
    printf("  -h, --help          Show this help message\n");
    printf("\nExamples:\n");
    printf("  %s -c /etc/args_enforcer/policy.json\n", prog_name);
    printf("  %s --list\n", prog_name);
    printf("  %s --stats\n", prog_name);
}

/* Main function */
int main(int argc, char *argv[]) {
    const char *config_path = "configs/optimized_policy.json";
    int verbose = 0;
    int list_only = 0;
    int stats_only = 0;
    int info_only = 0;
    int opt;
    
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"list", no_argument, 0, 'l'},
        {"stats", no_argument, 0, 's'},
        {"info", no_argument, 0, 'i'},
        {"verbose", no_argument, 0, 'v'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    // Parse command line arguments
    while ((opt = getopt_long(argc, argv, "c:lsivh", long_options, NULL)) != -1) {
        switch (opt) {
            case 'c':
                config_path = optarg;
                break;
            case 'l':
                list_only = 1;
                break;
            case 's':
                stats_only = 1;
                break;
            case 'i':
                info_only = 1;
                break;
            case 'v':
                verbose = 1;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    printf("Optimized eBPF Process Control Tool\n");
    printf("===================================\n");
    
    // Check if running as root
    if (geteuid() != 0) {
        log_error("This program must be run as root");
        return 1;
    }
    
    // Set up resource limits for eBPF
    if (bpf_setup_rlimits() < 0) {
        return 1;
    }
    
    // Load and parse policy configuration
    if (policy_load_optimized_config(config_path) < 0) {
        log_error("Failed to load policy configuration from %s", config_path);
        return 1;
    }
    
    // Handle list-only mode
    if (list_only) {
        policy_list_optimized();
        policy_optimized_cleanup();
        return 0;
    }
    
    // Load eBPF program
    if (bpf_load_optimized_enforcer_program(&obj, &events_fd) < 0) {
        log_error("Failed to load optimized eBPF program");
        policy_optimized_cleanup();
        return 1;
    }
    
    // Handle info-only mode
    if (info_only) {
        bpf_print_optimized_info(obj);
        bpf_cleanup_optimized_program(obj);
        policy_optimized_cleanup();
        return 0;
    }
    
    // Handle stats-only mode
    if (stats_only) {
        struct policy_stats stats;
        if (bpf_get_optimized_stats(obj, &stats) == 0) {
            printf("Statistics:\n");
            printf("  Total executions: %lu\n", stats.total_executions);
            printf("  Blocked executions: %lu\n", stats.blocked_executions);
            printf("  Allowed executions: %lu\n", stats.allowed_executions);
            printf("  Policy lookups: %lu\n", stats.policy_lookups);
            printf("  Policy matches: %lu\n", stats.policy_matches);
        }
        bpf_cleanup_optimized_program(obj);
        policy_optimized_cleanup();
        return 0;
    }
    
    // Apply policies to eBPF maps
    if (policy_apply_to_optimized_bpf(obj) < 0) {
        log_error("Failed to apply policies to eBPF maps");
        bpf_cleanup_optimized_program(obj);
        policy_optimized_cleanup();
        return 1;
    }
    
    // Attach eBPF program
    if (bpf_attach_optimized_program(obj) < 0) {
        log_error("Failed to attach eBPF program");
        bpf_cleanup_optimized_program(obj);
        policy_optimized_cleanup();
        return 1;
    }
    
    // Set up signal handlers
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    log_info("Optimized eBPF process control tool started");
    log_info("Loaded %d executable-based policies", policy_get_optimized_count());
    log_info("Processing first %d command-line arguments only", MAX_ARGS);
    log_info("Press Ctrl+C to stop");
    
    // Run main event loop
    run_event_loop();
    
    // Cleanup
    log_info("Cleaning up and exiting");
    bpf_cleanup_optimized_program(obj);
    policy_optimized_cleanup();
    
    return 0;
}
