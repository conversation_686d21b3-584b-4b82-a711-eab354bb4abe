/*
 * eBPF program loader implementation
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <errno.h>
#include <unistd.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>

#include "bpf_loader.h"
#include "logging.h"

#define BPF_PROGRAM_PATH "build/args_enforcer_improved.o"

/* Load eBPF program from object file */
int bpf_load_enforcer_program(struct bpf_object **obj, int *events_fd) {
    struct bpf_program *prog;
    struct bpf_map *events_map;
    int err;
    
    log_info("Loading eBPF program from %s", BPF_PROGRAM_PATH);
    
    // Check if object file exists
    if (access(BPF_PROGRAM_PATH, R_OK) != 0) {
        log_error("eBPF object file not found: %s", BPF_PROGRAM_PATH);
        log_error("Please run 'make' to build the eBPF program");
        return -1;
    }
    
    // Open eBPF object file
    *obj = bpf_object__open(BPF_PROGRAM_PATH);
    if (libbpf_get_error(*obj)) {
        log_error("Failed to open eBPF object file: %s", strerror(errno));
        return -1;
    }
    
    // Load eBPF object into kernel
    err = bpf_object__load(*obj);
    if (err) {
        log_error("Failed to load eBPF object: %s", strerror(-err));
        bpf_object__close(*obj);
        return -1;
    }
    
    // Find the main eBPF program
    prog = bpf_object__find_program_by_name(*obj, "trace_execve");
    if (!prog) {
        log_error("Failed to find trace_execve program in eBPF object");
        bpf_object__close(*obj);
        return -1;
    }
    
    // Attach the program to tracepoint
    err = bpf_program__attach(prog);
    if (err < 0) {
        log_error("Failed to attach eBPF program: %s", strerror(-err));
        bpf_object__close(*obj);
        return -1;
    }
    
    // Get events map file descriptor
    events_map = bpf_object__find_map_by_name(*obj, "events");
    if (!events_map) {
        log_error("Failed to find events map in eBPF object");
        bpf_object__close(*obj);
        return -1;
    }
    
    *events_fd = bpf_map__fd(events_map);
    if (*events_fd < 0) {
        log_error("Failed to get events map file descriptor");
        bpf_object__close(*obj);
        return -1;
    }
    
    log_info("Successfully loaded and attached eBPF program");
    return 0;
}

/* Get file descriptor for a specific map */
int bpf_get_map_fd(struct bpf_object *obj, const char *map_name) {
    struct bpf_map *map;
    
    map = bpf_object__find_map_by_name(obj, map_name);
    if (!map) {
        log_error("Failed to find map '%s' in eBPF object", map_name);
        return -1;
    }
    
    return bpf_map__fd(map);
}

/* Update policy map with new policy */
int bpf_update_policy_map(struct bpf_object *obj, unsigned int key, void *value) {
    int map_fd;
    
    map_fd = bpf_get_map_fd(obj, "policy_map");
    if (map_fd < 0) {
        return -1;
    }
    
    if (bpf_map_update_elem(map_fd, &key, value, BPF_ANY) != 0) {
        log_error("Failed to update policy map: %s", strerror(errno));
        return -1;
    }
    
    return 0;
}

/* Clear all entries from policy map */
int bpf_clear_policy_map(struct bpf_object *obj) {
    int map_fd;
    unsigned int key;
    
    map_fd = bpf_get_map_fd(obj, "policy_map");
    if (map_fd < 0) {
        return -1;
    }
    
    // Delete all possible keys
    for (key = 0; key < 1000; key++) {
        bpf_map_delete_elem(map_fd, &key);
    }
    
    log_debug("Cleared policy map");
    return 0;
}

/* Cleanup eBPF program resources */
void bpf_cleanup_program(struct bpf_object *obj) {
    if (obj) {
        bpf_object__close(obj);
        log_info("Cleaned up eBPF program resources");
    }
}
