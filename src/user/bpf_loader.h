/*
 * eBPF program loader header
 */

#ifndef BPF_LOADER_H
#define BPF_LOADER_H

#include <bpf/libbpf.h>

/* eBPF program loading and management */
int bpf_load_enforcer_program(struct bpf_object **obj, int *events_fd);
int bpf_attach_program(struct bpf_object *obj);
void bpf_cleanup_program(struct bpf_object *obj);

/* eBPF map management */
int bpf_get_map_fd(struct bpf_object *obj, const char *map_name);
int bpf_update_policy_map(struct bpf_object *obj, unsigned int key, void *value);
int bpf_clear_policy_map(struct bpf_object *obj);

#endif /* BPF_LOADER_H */
