/*
 * String Utilities Implementation - Inspired by FIM project
 * Safe and efficient string processing functions
 */

#include "string_utils.h"
#include <ctype.h>
#include <sys/time.h>

/* ========== String Utilities ========== */

const char* extract_filename(const char* path) {
    if (!path) return "";
    const char* filename = strrchr(path, '/');
    return filename ? filename + 1 : path;
}

int safe_strncpy(char *dest, const char *src, size_t dest_size) {
    if (!dest || !src || dest_size == 0) {
        return -1;
    }

    strncpy(dest, src, dest_size - 1);
    dest[dest_size - 1] = '\0';
    return 0;
}

int safe_strncat(char *dest, const char *src, size_t dest_size) {
    if (!dest || !src || dest_size == 0) {
        return -1;
    }

    size_t dest_len = strnlen(dest, dest_size);
    if (dest_len >= dest_size - 1) {
        return -1; // No space for concatenation
    }

    size_t remaining = dest_size - dest_len - 1;
    strncat(dest, src, remaining);
    dest[dest_size - 1] = '\0';
    return 0;
}

int pattern_match(const char *pattern, const char *text) {
    if (!pattern || !text) {
        return 0;
    }

    size_t pattern_len = strlen(pattern);
    size_t text_len = strlen(text);

    // Handle wildcard at end of pattern
    if (pattern_len > 0 && pattern[pattern_len - 1] == '*') {
        size_t prefix_len = pattern_len - 1;
        if (text_len >= prefix_len) {
            return strncmp(pattern, text, prefix_len) == 0;
        }
        return 0;
    }

    // Exact match
    return strcmp(pattern, text) == 0;
}

int argument_match(const char *arg, const char *pattern) {
    if (!arg || !pattern) {
        return 0;
    }

    // Wildcard matching
    if (pattern_match(pattern, arg)) {
        return 1;
    }

    // Exact match
    if (strcmp(arg, pattern) == 0) {
        return 1;
    }

    // Substring match for patterns like "-o StrictHostKeyChecking=yes"
    if (strstr(arg, pattern) != NULL) {
        return 1;
    }

    return 0;
}

int parse_cmdline_args(const char *cmdline, char args[][256], int max_args, int arg_len) {
    if (!cmdline || !args || max_args <= 0 || arg_len <= 0) {
        return -1;
    }

    int argc = 0;
    char *cmdline_copy = strdup(cmdline);
    if (!cmdline_copy) {
        return -1;
    }

    char *token = strtok(cmdline_copy, " \t\n");
    while (token && argc < max_args) {
        if (safe_strncpy(args[argc], token, arg_len) == 0) {
            argc++;
        }
        token = strtok(NULL, " \t\n");
    }

    free(cmdline_copy);
    return argc;
}

int check_arg_exists(char args[][256], int argc, const char *target, int arg_len) {
    if (!args || !target || argc <= 0 || arg_len <= 0) {
        return 0;
    }

    for (int i = 0; i < argc; i++) {
        if (argument_match(args[i], target)) {
            return 1;
        }
    }
    return 0;
}

int check_arg_combination(char args[][256], int argc, const char *target, int arg_len) {
    if (!args || !target || argc <= 0 || arg_len <= 0) {
        return 0;
    }

    // Check for single argument match first
    if (check_arg_exists(args, argc, target, arg_len)) {
        return 1;
    }

    // Check for split arguments (e.g., "-o" "StrictHostKeyChecking=no")
    for (int i = 0; i < argc - 1; i++) {
        if (strcmp(args[i], "-o") == 0 && i + 1 < argc) {
            // Skip "-o " prefix if present in target
            const char *pattern = target;
            if (strncmp(target, "-o ", 3) == 0) {
                pattern = target + 3;
            }
            if (argument_match(args[i + 1], pattern)) {
                return 1;
            }
        }
    }

    return 0;
}

/* ========== Time Utilities ========== */

void format_timestamp(__u64 timestamp, char *buffer, size_t buffer_size) {
    if (!buffer || buffer_size == 0) {
        return;
    }

    time_t sec = timestamp / 1000000000ULL;
    struct tm *tm_info = localtime(&sec);

    if (tm_info) {
        strftime(buffer, buffer_size, "%Y-%m-%d %H:%M:%S", tm_info);
    } else {
        safe_strncpy(buffer, "Invalid timestamp", buffer_size);
    }
}

__u64 get_current_timestamp(void) {
    struct timespec ts;
    if (clock_gettime(CLOCK_REALTIME, &ts) == 0) {
        return (__u64)ts.tv_sec * 1000000000ULL + (__u64)ts.tv_nsec;
    }
    return 0;
}

/* ========== Hash Utilities ========== */

__u32 hash_string(const char *str) {
    if (!str) {
        return 0;
    }

    __u32 hash = 5381;
    int c;

    while ((c = *str++)) {
        hash = ((hash << 5) + hash) + c; // hash * 33 + c
    }

    return hash;
}

__u32 hash_args_array(char args[][256], int argc, int arg_len) {
    if (!args || argc <= 0 || arg_len <= 0) {
        return 0;
    }

    __u32 hash = 5381;

    for (int i = 0; i < argc; i++) {
        for (int j = 0; j < arg_len && args[i][j] != '\0'; j++) {
            hash = ((hash << 5) + hash) + args[i][j];
        }
    }

    return hash;
}

/* ========== Validation Utilities ========== */

int validate_string(const char *str, size_t max_len) {
    if (!str) {
        return 0;
    }

    size_t len = strnlen(str, max_len + 1);
    return (len <= max_len) ? 1 : 0;
}

int sanitize_string(char *str, size_t max_len) {
    if (!str || max_len == 0) {
        return -1;
    }

    size_t len = strnlen(str, max_len);
    
    for (size_t i = 0; i < len; i++) {
        // Remove control characters except tab and newline
        if (iscntrl(str[i]) && str[i] != '\t' && str[i] != '\n') {
            str[i] = '?';
        }
    }

    // Ensure null termination
    if (len == max_len) {
        str[max_len - 1] = '\0';
    }

    return 0;
}
