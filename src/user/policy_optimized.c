/*
 * Optimized Policy Management for eBPF Process Control Tool
 * Supports executable-based policy storage with O(1) lookup
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <errno.h>
#include <time.h>
#include <json-c/json.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include "../kernel/args_enforcer_optimized.h"

/* Simple logging functions */
#define log_info(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define log_error(fmt, ...) fprintf(stderr, "[ERROR] " fmt "\n", ##__VA_ARGS__)
#define log_warning(fmt, ...) printf("[WARNING] " fmt "\n", ##__VA_ARGS__)
#define log_debug(fmt, ...) printf("[DEBUG] " fmt "\n", ##__VA_ARGS__)

/* Global policy storage */
static struct executable_policy policies[MAX_POLICIES];
static int policy_count = 0;

/* Helper function to normalize executable path */
static int normalize_executable_path(const char *path, char *normalized, size_t max_len) {
    if (!path || !normalized || max_len == 0) {
        return -1;
    }
    
    // Handle relative paths by converting to absolute
    if (path[0] != '/') {
        // For now, assume it's in /usr/bin or /bin
        if (strstr(path, "/") == NULL) {
            // Just executable name, try common paths
            snprintf(normalized, max_len, "/usr/bin/%s", path);
        } else {
            strncpy(normalized, path, max_len - 1);
            normalized[max_len - 1] = '\0';
        }
    } else {
        strncpy(normalized, path, max_len - 1);
        normalized[max_len - 1] = '\0';
    }
    
    return 0;
}

/* Load policy from JSON configuration */
int policy_load_optimized_config(const char *config_path) {
    FILE *file;
    char *buffer;
    long file_size;
    json_object *root, *policies_obj, *policy_obj;
    json_object *name_obj, *include_obj, *exclude_obj;
    struct executable_policy *policy;
    int i, j;

    log_info("Loading optimized policy configuration from %s", config_path);

    // Read JSON file
    file = fopen(config_path, "r");
    if (!file) {
        log_error("Failed to open config file: %s", strerror(errno));
        return -1;
    }

    fseek(file, 0, SEEK_END);
    file_size = ftell(file);
    fseek(file, 0, SEEK_SET);

    buffer = malloc(file_size + 1);
    if (!buffer) {
        log_error("Failed to allocate memory for config file");
        fclose(file);
        return -1;
    }

    fread(buffer, 1, file_size, file);
    buffer[file_size] = '\0';
    fclose(file);

    // Parse JSON
    root = json_tokener_parse(buffer);
    free(buffer);

    if (!root) {
        log_error("Failed to parse JSON configuration");
        return -1;
    }

    // Get policies object
    if (!json_object_object_get_ex(root, "policies", &policies_obj)) {
        log_error("No 'policies' object found in configuration");
        json_object_put(root);
        return -1;
    }

    policy_count = 0;

    // Iterate through executable-based policies
    json_object_object_foreach(policies_obj, executable_path, policy_obj) {
        if (policy_count >= MAX_POLICIES) {
            log_warning("Maximum number of policies reached, skipping remaining");
            break;
        }

        policy = &policies[policy_count];
        memset(policy, 0, sizeof(*policy));

        // Normalize and set executable path
        if (normalize_executable_path(executable_path, policy->executable_path, 
                                    sizeof(policy->executable_path)) < 0) {
            log_warning("Invalid executable path: %s", executable_path);
            continue;
        }

        // Get policy name
        if (json_object_object_get_ex(policy_obj, "name", &name_obj)) {
            strncpy(policy->policy_name, json_object_get_string(name_obj), 
                   MAX_POLICY_NAME_LEN - 1);
            policy->policy_name[MAX_POLICY_NAME_LEN - 1] = '\0';
        } else {
            snprintf(policy->policy_name, MAX_POLICY_NAME_LEN, "policy_%d", policy_count);
        }

        // Load include arguments
        policy->include_count = 0;
        if (json_object_object_get_ex(policy_obj, "include", &include_obj)) {
            int include_len = json_object_array_length(include_obj);
            for (i = 0; i < include_len && i < MAX_POLICY_ARGS; i++) {
                json_object *arg_obj = json_object_array_get_idx(include_obj, i);
                if (arg_obj) {
                    strncpy(policy->include_args[i], json_object_get_string(arg_obj), 
                           MAX_ARG_LEN - 1);
                    policy->include_args[i][MAX_ARG_LEN - 1] = '\0';
                    policy->include_count++;
                }
            }
        }

        // Load exclude arguments
        policy->exclude_count = 0;
        if (json_object_object_get_ex(policy_obj, "exclude", &exclude_obj)) {
            int exclude_len = json_object_array_length(exclude_obj);
            for (i = 0; i < exclude_len && i < MAX_POLICY_ARGS; i++) {
                json_object *arg_obj = json_object_array_get_idx(exclude_obj, i);
                if (arg_obj) {
                    strncpy(policy->exclude_args[i], json_object_get_string(arg_obj), 
                           MAX_ARG_LEN - 1);
                    policy->exclude_args[i][MAX_ARG_LEN - 1] = '\0';
                    policy->exclude_count++;
                }
            }
        }

        policy->enabled = 1;
        policy->last_updated = time(NULL);
        policy->match_count = 0;

        log_info("Loaded policy '%s' for executable '%s' (include: %d, exclude: %d)",
                 policy->policy_name, policy->executable_path, 
                 policy->include_count, policy->exclude_count);

        policy_count++;
    }

    json_object_put(root);
    log_info("Loaded %d optimized policies", policy_count);
    return 0;
}

/* Apply policies to eBPF executable-based map */
int policy_apply_to_optimized_bpf(struct bpf_object *obj) {
    struct bpf_map *policy_map;
    int map_fd;
    int i;

    log_info("Applying %d policies to optimized eBPF maps", policy_count);

    // Find executable policy map
    policy_map = bpf_object__find_map_by_name(obj, "executable_policy_map");
    if (!policy_map) {
        log_error("Failed to find executable_policy_map in eBPF object");
        return -1;
    }

    map_fd = bpf_map__fd(policy_map);
    if (map_fd < 0) {
        log_error("Failed to get executable_policy_map file descriptor");
        return -1;
    }

    // Clear existing policies in map
    // Note: For hash maps, we need to iterate and delete existing keys
    // For simplicity, we'll just overwrite with new policies

    // Apply each policy using executable path as key
    for (i = 0; i < policy_count; i++) {
        struct executable_policy *policy = &policies[i];
        
        if (bpf_map_update_elem(map_fd, policy->executable_path, policy, BPF_ANY) < 0) {
            log_error("Failed to update policy for executable '%s': %s", 
                     policy->executable_path, strerror(errno));
            return -1;
        }
        
        log_debug("Applied policy '%s' for executable '%s'", 
                 policy->policy_name, policy->executable_path);
    }

    log_info("Successfully applied %d policies to eBPF maps", policy_count);
    return 0;
}

/* Add a new executable-based policy */
int policy_add_executable_rule(const char *executable_path, const char *policy_name,
                              const char **include_args, int include_count,
                              const char **exclude_args, int exclude_count) {
    if (policy_count >= MAX_POLICIES) {
        log_error("Maximum number of policies reached");
        return -1;
    }

    struct executable_policy *policy = &policies[policy_count];
    memset(policy, 0, sizeof(*policy));

    // Normalize and set executable path
    if (normalize_executable_path(executable_path, policy->executable_path, 
                                sizeof(policy->executable_path)) < 0) {
        log_error("Invalid executable path: %s", executable_path);
        return -1;
    }

    strncpy(policy->policy_name, policy_name, MAX_POLICY_NAME_LEN - 1);
    policy->policy_name[MAX_POLICY_NAME_LEN - 1] = '\0';

    // Add include arguments
    policy->include_count = 0;
    for (int i = 0; i < include_count && i < MAX_POLICY_ARGS; i++) {
        strncpy(policy->include_args[i], include_args[i], MAX_ARG_LEN - 1);
        policy->include_args[i][MAX_ARG_LEN - 1] = '\0';
        policy->include_count++;
    }

    // Add exclude arguments
    policy->exclude_count = 0;
    for (int i = 0; i < exclude_count && i < MAX_POLICY_ARGS; i++) {
        strncpy(policy->exclude_args[i], exclude_args[i], MAX_ARG_LEN - 1);
        policy->exclude_args[i][MAX_ARG_LEN - 1] = '\0';
        policy->exclude_count++;
    }

    policy->enabled = 1;
    policy->last_updated = time(NULL);
    policy->match_count = 0;

    policy_count++;
    log_info("Added policy '%s' for executable '%s'", policy_name, executable_path);
    return 0;
}

/* List all executable-based policies */
void policy_list_optimized(void) {
    printf("Optimized Executable-Based Policies (%d total):\n", policy_count);
    printf("%-40s %-20s %-8s %-8s %-10s\n", 
           "Executable", "Policy Name", "Include", "Exclude", "Matches");
    printf("%-40s %-20s %-8s %-8s %-10s\n", 
           "----------------------------------------", 
           "--------------------", 
           "--------", "--------", "----------");

    for (int i = 0; i < policy_count; i++) {
        struct executable_policy *policy = &policies[i];
        printf("%-40s %-20s %-8d %-8d %-10u\n",
               policy->executable_path,
               policy->policy_name,
               policy->include_count,
               policy->exclude_count,
               policy->match_count);
    }
}

/* Get policy count */
int policy_get_optimized_count(void) {
    return policy_count;
}

/* Cleanup policy resources */
void policy_optimized_cleanup(void) {
    policy_count = 0;
    memset(policies, 0, sizeof(policies));
}
