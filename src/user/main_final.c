/*
 * Final eBPF Process Control Tool - Main Program
 * Features:
 * 1. Limited to first 5 command-line arguments stored in separate variables
 * 2. Executable-based policy storage for O(1) lookup
 * 3. Space-separated argument strings in policies
 * 4. No stack overflow issues
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>
#include <signal.h>
#include <errno.h>
#include <getopt.h>
#include <sys/resource.h>
#include <bpf/libbpf.h>
#include <bpf/bpf.h>
#include "../kernel/args_enforcer_final.h"

/* External function declarations */
extern int policy_load_final_config(const char *config_path);
extern int policy_apply_to_final_bpf(struct bpf_object *obj);
extern void policy_list_final(void);
extern void policy_final_cleanup(void);
extern int policy_get_final_count(void);

/* Simple logging functions */
#define log_info(fmt, ...) printf("[INFO] " fmt "\n", ##__VA_ARGS__)
#define log_error(fmt, ...) fprintf(stderr, "[ERROR] " fmt "\n", ##__VA_ARGS__)

/* Global variables */
static struct bpf_object *obj = NULL;
static int events_fd = -1;
static volatile int running = 1;

/* Signal handler */
static void signal_handler(int sig) {
    log_info("Received signal %d, shutting down...", sig);
    running = 0;
}

/* Handle events from eBPF program */
static int handle_event(void *ctx, void *data, size_t data_sz) {
    struct exec_event *event = (struct exec_event *)data;
    
    if (data_sz < sizeof(*event)) {
        log_error("Invalid event size: %zu", data_sz);
        return 0;
    }
    
    printf("[%s] PID: %u, UID: %u, Executable: %s\n",
           event->action ? "BLOCKED" : "ALLOWED",
           event->pid,
           event->uid,
           event->filename);
    
    if (event->action) {
        printf("  Command: %s", event->comm);
        if (event->argc >= 1 && event->arg1[0]) printf(" %s", event->arg1);
        if (event->argc >= 2 && event->arg2[0]) printf(" %s", event->arg2);
        if (event->argc >= 3 && event->arg3[0]) printf(" %s", event->arg3);
        if (event->argc >= 4 && event->arg4[0]) printf(" %s", event->arg4);
        if (event->argc >= 5 && event->arg5[0]) printf(" %s", event->arg5);
        printf("\n");
    }
    
    return 0;
}

/* Load final eBPF program */
int load_final_program(void) {
    struct bpf_map *events_map;
    int err;
    
    log_info("Loading final eBPF program");
    
    // Open eBPF object file (try minimal version first, then final)
    obj = bpf_object__open("build/args_enforcer_minimal.o");
    if (libbpf_get_error(obj)) {
        obj = bpf_object__open("build/args_enforcer_final.o");
    }
    if (libbpf_get_error(obj)) {
        log_error("Failed to open eBPF object file");
        return -1;
    }
    
    // Load eBPF program into kernel
    err = bpf_object__load(obj);
    if (err) {
        log_error("Failed to load eBPF program: %s", strerror(-err));
        bpf_object__close(obj);
        return -1;
    }
    
    // Find the events map
    events_map = bpf_object__find_map_by_name(obj, "events");
    if (!events_map) {
        log_error("Failed to find events map");
        bpf_object__close(obj);
        return -1;
    }
    
    events_fd = bpf_map__fd(events_map);
    if (events_fd < 0) {
        log_error("Failed to get events map file descriptor");
        bpf_object__close(obj);
        return -1;
    }
    
    log_info("Successfully loaded final eBPF program");
    return 0;
}

/* Attach eBPF program */
int attach_final_program(void) {
    struct bpf_program *prog;
    struct bpf_link *link;
    
    log_info("Attaching final eBPF program");
    
    prog = bpf_object__find_program_by_name(obj, "trace_execve");
    if (!prog) {
        log_error("Failed to find trace_execve program");
        return -1;
    }
    
    link = bpf_program__attach(prog);
    if (libbpf_get_error(link)) {
        log_error("Failed to attach eBPF program");
        return -1;
    }
    
    log_info("Successfully attached final eBPF program");
    return 0;
}

/* Main event loop */
static int run_event_loop(void) {
    struct ring_buffer *rb;
    int err;
    
    log_info("Starting final event loop");
    
    rb = ring_buffer__new(events_fd, handle_event, NULL, NULL);
    if (!rb) {
        log_error("Failed to create ring buffer");
        return -1;
    }
    
    while (running) {
        err = ring_buffer__poll(rb, 100);
        if (err == -EINTR) {
            break;
        }
        if (err < 0) {
            log_error("Error polling ring buffer: %d", err);
            break;
        }
    }
    
    ring_buffer__free(rb);
    log_info("Event loop terminated");
    return 0;
}

/* Print usage */
static void print_usage(const char *prog_name) {
    printf("Usage: %s [OPTIONS]\n", prog_name);
    printf("\nFinal eBPF Process Control Tool\n");
    printf("Features: 5-arg limit, separate arg storage, space-separated policies\n\n");
    printf("Options:\n");
    printf("  -c, --config FILE    Configuration file (default: configs/optimized_policy.json)\n");
    printf("  -l, --list          List loaded policies and exit\n");
    printf("  -h, --help          Show this help message\n");
}

/* Set up resource limits */
int setup_rlimits(void) {
    struct rlimit rlim = {
        .rlim_cur = RLIM_INFINITY,
        .rlim_max = RLIM_INFINITY,
    };
    
    if (setrlimit(RLIMIT_MEMLOCK, &rlim)) {
        log_error("Failed to increase RLIMIT_MEMLOCK: %s", strerror(errno));
        return -1;
    }
    
    return 0;
}

/* Main function */
int main(int argc, char *argv[]) {
    const char *config_path = "configs/optimized_policy.json";
    int list_only = 0;
    int opt;
    
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"list", no_argument, 0, 'l'},
        {"help", no_argument, 0, 'h'},
        {0, 0, 0, 0}
    };
    
    while ((opt = getopt_long(argc, argv, "c:lh", long_options, NULL)) != -1) {
        switch (opt) {
            case 'c':
                config_path = optarg;
                break;
            case 'l':
                list_only = 1;
                break;
            case 'h':
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    printf("Final eBPF Process Control Tool\n");
    printf("===============================\n");
    
    if (geteuid() != 0) {
        log_error("This program must be run as root");
        return 1;
    }
    
    if (setup_rlimits() < 0) {
        return 1;
    }
    
    if (policy_load_final_config(config_path) < 0) {
        return 1;
    }
    
    if (list_only) {
        policy_list_final();
        policy_final_cleanup();
        return 0;
    }
    
    if (load_final_program() < 0) {
        policy_final_cleanup();
        return 1;
    }
    
    if (policy_apply_to_final_bpf(obj) < 0) {
        bpf_object__close(obj);
        policy_final_cleanup();
        return 1;
    }
    
    if (attach_final_program() < 0) {
        bpf_object__close(obj);
        policy_final_cleanup();
        return 1;
    }
    
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    log_info("Final eBPF process control tool started");
    log_info("Processing first %d command-line arguments only", MAX_ARGS);
    log_info("Arguments stored in separate variables (arg1-arg5)");
    log_info("Press Ctrl+C to stop");
    
    run_event_loop();
    
    log_info("Cleaning up and exiting");
    bpf_object__close(obj);
    policy_final_cleanup();
    
    return 0;
}
