/*
 * String Utilities Header - Inspired by FIM project
 * Safe and efficient string processing functions
 */

#ifndef STRING_UTILS_H
#define STRING_UTILS_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdint.h>
#include <time.h>
#include <sys/types.h>
#include <linux/types.h>

/* ========== String Utilities ========== */

/**
 * Extract filename from full path (inspired by FIM)
 * @param path: Full file path
 * @return: Pointer to filename part (not allocated, points into path)
 */
const char* extract_filename(const char* path);

/**
 * Safe string copy with bounds checking (inspired by FIM)
 * @param dest: Destination buffer
 * @param src: Source string
 * @param dest_size: Size of destination buffer
 * @return: 0 on success, -1 on error
 */
int safe_strncpy(char *dest, const char *src, size_t dest_size);

/**
 * Safe string concatenation with bounds checking
 * @param dest: Destination buffer
 * @param src: Source string to append
 * @param dest_size: Size of destination buffer
 * @return: 0 on success, -1 on error
 */
int safe_strncat(char *dest, const char *src, size_t dest_size);

/**
 * Check if string matches pattern (supports wildcards)
 * @param pattern: Pattern string (may contain * wildcard)
 * @param text: Text to match against
 * @return: 1 if match, 0 if not
 */
int pattern_match(const char *pattern, const char *text);

/**
 * Check if argument matches pattern with enhanced logic
 * @param arg: Argument string
 * @param pattern: Pattern to match
 * @return: 1 if match, 0 if not
 */
int argument_match(const char *arg, const char *pattern);

/**
 * Parse command line string into arguments
 * @param cmdline: Command line string
 * @param args: Output array of argument strings
 * @param max_args: Maximum number of arguments
 * @param arg_len: Maximum length of each argument
 * @return: Number of arguments parsed, -1 on error
 */
int parse_cmdline_args(const char *cmdline, char args[][256], int max_args, int arg_len);

/**
 * Check if argument exists in argument list
 * @param args: Array of argument strings
 * @param argc: Number of arguments
 * @param target: Target argument to find
 * @param arg_len: Maximum length of each argument
 * @return: 1 if found, 0 if not
 */
int check_arg_exists(char args[][256], int argc, const char *target, int arg_len);

/**
 * Check for argument combinations (e.g., "-o" followed by "value")
 * @param args: Array of argument strings
 * @param argc: Number of arguments
 * @param target: Target argument pattern
 * @param arg_len: Maximum length of each argument
 * @return: 1 if found, 0 if not
 */
int check_arg_combination(char args[][256], int argc, const char *target, int arg_len);

/* ========== Time Utilities (inspired by FIM) ========== */

/**
 * Format timestamp for logging
 * @param timestamp: Timestamp in nanoseconds
 * @param buffer: Output buffer
 * @param buffer_size: Size of output buffer
 */
void format_timestamp(__u64 timestamp, char *buffer, size_t buffer_size);

/**
 * Get current timestamp in nanoseconds
 * @return: Current timestamp
 */
__u64 get_current_timestamp(void);

/* ========== Hash Utilities ========== */

/**
 * Simple hash function for strings (DJB2 algorithm)
 * @param str: String to hash
 * @return: Hash value
 */
__u32 hash_string(const char *str);

/**
 * Hash function for argument arrays
 * @param args: Array of argument strings
 * @param argc: Number of arguments
 * @param arg_len: Maximum length of each argument
 * @return: Hash value
 */
__u32 hash_args_array(char args[][256], int argc, int arg_len);

/* ========== Validation Utilities ========== */

/**
 * Validate string is safe for processing
 * @param str: String to validate
 * @param max_len: Maximum allowed length
 * @return: 1 if valid, 0 if not
 */
int validate_string(const char *str, size_t max_len);

/**
 * Sanitize string by removing dangerous characters
 * @param str: String to sanitize (modified in place)
 * @param max_len: Maximum length
 * @return: 0 on success, -1 on error
 */
int sanitize_string(char *str, size_t max_len);

#endif /* STRING_UTILS_H */
