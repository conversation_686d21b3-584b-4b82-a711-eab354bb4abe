{"description": "Optimized executable-based policy configuration", "version": "2.0", "policies": {"/usr/bin/ssh": {"name": "ssh_security", "description": "Enforce secure SSH connections", "include": ["-o StrictHostKeyChecking=yes"], "exclude": ["-o StrictHostKeyChecking=no", "-o UserKnownHostsFile=/dev/null", "-o GlobalKnownHostsFile=/dev/null"]}, "/usr/bin/curl": {"name": "curl_security", "description": "Prevent insecure curl usage", "include": [], "exclude": ["-k", "--insecure", "--disable-cert-revocation-checks"]}, "/usr/bin/wget": {"name": "wget_security", "description": "Prevent insecure wget usage", "include": [], "exclude": ["--no-check-certificate", "--no-verify-certificate"]}, "/usr/bin/docker": {"name": "docker_security", "description": "Enforce secure Docker practices", "include": [], "exclude": ["--privileged", "--cap-add=ALL", "--security-opt=no-new-privileges=false"]}, "/usr/bin/sudo": {"name": "sudo_restrictions", "description": "Restrict dangerous sudo usage", "include": [], "exclude": ["-u root", "--preserve-env=PATH"]}, "/bin/bash": {"name": "bash_security", "description": "Prevent dangerous bash usage", "include": [], "exclude": ["-c rm*", "-c sudo*"]}, "/usr/bin/python": {"name": "python_security", "description": "Restrict dangerous Python execution", "include": [], "exclude": ["-c exec*", "-c eval*", "-c __import__*"]}, "/usr/bin/python3": {"name": "python3_security", "description": "Restrict dangerous Python3 execution", "include": [], "exclude": ["-c exec*", "-c eval*", "-c __import__*"]}}, "settings": {"max_args_processed": 5, "verbose_logging": false, "performance_mode": true, "cache_policies": true}}