# Makefile for Optimized eBPF Process Control Tool

CC := clang
ARCH := $(shell uname -m | sed 's/x86_64/x86/' | sed 's/aarch64/arm64/')

SRC_DIR := src
KERNEL_DIR := $(SRC_DIR)/kernel
USER_DIR := $(SRC_DIR)/user
BUILD_DIR := build
BIN_DIR := bin

# eBPF compilation flags
EBPF_CFLAGS := -O2 -g -Wall \
               -target bpf -D__TARGET_ARCH_$(ARCH) \
               -I/usr/include/$(shell uname -m)-linux-gnu \
               -I$(KERNEL_DIR)

# User-space compilation flags
USER_CFLAGS := -O2 -g -Wall -I$(KERNEL_DIR)
USER_LDFLAGS := -lbpf -lelf -lz -ljson-c

# Source files
EBPF_SOURCE := $(KERNEL_DIR)/args_enforcer_optimized.c
USER_SOURCES := $(USER_DIR)/main_optimized.c \
                $(USER_DIR)/policy_optimized.c \
                $(USER_DIR)/bpf_loader_optimized.c

# Target files
EBPF_TARGET := $(BUILD_DIR)/args_enforcer_optimized.o
USER_TARGET := $(BIN_DIR)/args-enforcer-optimized

.PHONY: all clean test install uninstall

all: $(EBPF_TARGET) $(USER_TARGET)

$(BUILD_DIR):
	mkdir -p $(BUILD_DIR)

$(BIN_DIR):
	mkdir -p $(BIN_DIR)

# Build eBPF program
$(EBPF_TARGET): $(EBPF_SOURCE) | $(BUILD_DIR)
	@echo "Building optimized eBPF program..."
	$(CC) $(EBPF_CFLAGS) -c $< -o $@
	@echo "eBPF program built successfully"

# Build user-space program
$(USER_TARGET): $(USER_SOURCES) $(EBPF_TARGET) | $(BIN_DIR)
	@echo "Building optimized user-space program..."
	$(CC) $(USER_CFLAGS) $(USER_SOURCES) -o $@ $(USER_LDFLAGS)
	@echo "User-space program built successfully"

# Test the optimized implementation
test: $(EBPF_TARGET) $(USER_TARGET)
	@echo "Testing optimized eBPF implementation..."
	@echo "1. Checking eBPF object file..."
	file $(EBPF_TARGET)
	@echo "2. Checking user-space binary..."
	file $(USER_TARGET)
	@echo "3. Testing policy loading..."
	$(USER_TARGET) --config configs/optimized_policy.json --list
	@echo "4. Testing program info..."
	$(USER_TARGET) --info
	@echo "Optimized implementation tests completed"

# Install the optimized version
install: $(USER_TARGET)
	@echo "Installing optimized eBPF process control tool..."
	sudo cp $(USER_TARGET) /usr/local/bin/
	sudo mkdir -p /etc/args_enforcer
	sudo cp configs/optimized_policy.json /etc/args_enforcer/
	sudo chmod +x /usr/local/bin/args-enforcer-optimized
	@echo "Installation completed"

# Uninstall
uninstall:
	@echo "Uninstalling optimized eBPF process control tool..."
	sudo rm -f /usr/local/bin/args-enforcer-optimized
	sudo rm -rf /etc/args_enforcer
	@echo "Uninstallation completed"

# Clean build artifacts
clean:
	@echo "Cleaning build artifacts..."
	rm -rf $(BUILD_DIR) $(BIN_DIR)
	@echo "Clean completed"

# Development targets
dev-build: all
	@echo "Development build completed"

dev-test: test
	@echo "Running development tests..."
	@echo "Testing with verbose output..."
	$(USER_TARGET) --config configs/optimized_policy.json --verbose --list

# Performance comparison (requires both versions)
compare: $(USER_TARGET)
	@echo "Performance comparison between original and optimized versions:"
	@echo "Optimized version features:"
	@echo "  - Limited to first 5 command-line arguments (vs 64)"
	@echo "  - Executable-based policy storage with O(1) lookup (vs O(n) iteration)"
	@echo "  - Reduced policy structure size"
	@echo "  - Direct hash map lookup"
	@echo ""
	@echo "Memory usage comparison:"
	@echo "Original: MAX_ARGS=64, policy iteration"
	@echo "Optimized: MAX_ARGS=5, direct lookup"
	@echo ""
	@echo "Run 'make benchmark' to perform actual performance tests"

# Benchmark (requires root privileges)
benchmark: $(USER_TARGET)
	@echo "Running benchmark tests (requires root)..."
	@echo "Note: This will load the eBPF program and test policy lookups"
	sudo $(USER_TARGET) --config configs/optimized_policy.json --stats

# Help target
help:
	@echo "Optimized eBPF Process Control Tool - Build System"
	@echo "=================================================="
	@echo ""
	@echo "Targets:"
	@echo "  all        - Build both eBPF and user-space programs"
	@echo "  test       - Test the optimized implementation"
	@echo "  install    - Install the optimized version system-wide"
	@echo "  uninstall  - Remove the installed version"
	@echo "  clean      - Clean build artifacts"
	@echo "  dev-build  - Development build with extra checks"
	@echo "  dev-test   - Development testing with verbose output"
	@echo "  compare    - Show performance comparison information"
	@echo "  benchmark  - Run performance benchmark (requires root)"
	@echo "  help       - Show this help message"
	@echo ""
	@echo "Optimizations:"
	@echo "  - Limited to first 5 command-line arguments for performance"
	@echo "  - Executable-based policy storage for O(1) lookup"
	@echo "  - Reduced memory footprint and improved cache locality"
	@echo ""
	@echo "Usage:"
	@echo "  make all && sudo make test"
	@echo "  sudo make install"
	@echo "  sudo args-enforcer-optimized --config /etc/args_enforcer/optimized_policy.json"

# Debug target
debug: EBPF_CFLAGS += -DDEBUG
debug: USER_CFLAGS += -DDEBUG -g3
debug: all
	@echo "Debug build completed with extra debugging information"

# Check dependencies
check-deps:
	@echo "Checking dependencies..."
	@which clang > /dev/null || (echo "Error: clang not found" && exit 1)
	@pkg-config --exists libbpf || (echo "Error: libbpf not found" && exit 1)
	@pkg-config --exists json-c || (echo "Error: json-c not found" && exit 1)
	@echo "All dependencies satisfied"

# Show build information
info:
	@echo "Build Information:"
	@echo "=================="
	@echo "Architecture: $(ARCH)"
	@echo "Compiler: $(CC)"
	@echo "eBPF Flags: $(EBPF_CFLAGS)"
	@echo "User Flags: $(USER_CFLAGS)"
	@echo "Libraries: $(USER_LDFLAGS)"
	@echo "Source Files:"
	@echo "  eBPF: $(EBPF_SOURCE)"
	@echo "  User: $(USER_SOURCES)"
	@echo "Target Files:"
	@echo "  eBPF: $(EBPF_TARGET)"
	@echo "  User: $(USER_TARGET)"
